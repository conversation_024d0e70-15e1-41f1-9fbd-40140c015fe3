<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f6f6f6;
            padding: 20px;
            color: #333;
        }

        .email-container {
            background-color: #f1efef;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
            /* Center all text inside email container */
        }

        .logo img {
            max-height: 80px;
            display: block;
            margin: 0 auto 20px;
            /* Center image horizontally and add bottom margin */
        }

        h1 {
            color: #2c3e50;
            margin-top: 0;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            text-align: left;
            /* But align paragraph text back to left */
        }

        strong {
            color: #2c3e50;
        }
        .login-button {
            display: inline-block;
            background-color: #b8e6f2 !important;
            color: grey !important;
            padding: 12px 24px;
            text-decoration: none !important;
            border-radius: 4px;
            margin-top: 20px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        /* .login-button:hover {
            background-color: #1a252f;
        } */
    </style>
</head>

<body>
    <div class="email-container">
        <p>Hello, your password has been reset!</p>

        <p>Your new password is: <b><?php echo e($password); ?></b></p>

        <p><b>Important:</b> You will be required to change your password when you log in to the system. This is a
            security measure to ensure the protection of your account.</p>

        <div style="text-align: center; margin-top: 30px; margin-bottom: 30px;">
            <?php if(isset($user) && $user->role == \App\Models\User::ROLE_PROVIDER): ?>
                <a href="https://<?php echo e(config('app.provider_portal_domain')); ?>" class="login-button">Login to Portal</a>
            <?php else: ?>
                <a href="https://<?php echo e(config('app.staff_portal_domain')); ?>" class="login-button">Login to Portal</a>
            <?php endif; ?>
        </div>

        <p>© <?php echo e(now()->format('Y')); ?> <?php echo e(config('app.name')); ?>. All rights reserved.</p>
    </div>
</body>

</html>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/emails/forgot-password.blade.php ENDPATH**/ ?>