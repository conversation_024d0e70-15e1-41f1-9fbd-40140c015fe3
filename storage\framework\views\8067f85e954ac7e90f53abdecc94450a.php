<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'steps' => [],
    'currentStep' => 1,
    'editMode' => false,
    'gap' => '4rem'
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'steps' => [],
    'currentStep' => 1,
    'editMode' => false,
    'gap' => '4rem'
]); ?>
<?php foreach (array_filter(([
    'steps' => [],
    'currentStep' => 1,
    'editMode' => false,
    'gap' => '4rem'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-center" style="gap: <?php echo e($gap); ?>;">
            <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stepNumber => $stepLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="text-center">
                    <?php if($editMode): ?>
                        <button
                            type="button"
                            wire:click="goToStep(<?php echo e($stepNumber); ?>)"
                            class="btn p-0 border-0"
                            style="background: none;"
                        >
                            <div class="d-flex flex-column align-items-center">
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px; background-color: <?php echo e($currentStep === $stepNumber ? '#e9c5ff' : '#d1d5db'); ?>; color: <?php echo e($currentStep === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; font-weight: 600; font-size: 1.1rem;">
                                    <?php echo e($stepNumber); ?>

                                </div>
                                <div class="mt-2" style="font-size: 0.875rem; color: <?php echo e($currentStep === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; white-space: nowrap;">
                                    <?php echo e($stepLabel); ?>

                                </div>
                            </div>
                        </button>
                    <?php else: ?>
                        <div class="d-flex flex-column align-items-center">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px; background-color: <?php echo e($currentStep === $stepNumber ? '#e9c5ff' : '#d1d5db'); ?>; color: <?php echo e($currentStep === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; font-weight: 600; font-size: 1.1rem;">
                                <?php echo e($stepNumber); ?>

                            </div>
                            <div class="mt-2" style="font-size: 0.875rem; color: <?php echo e($currentStep === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; white-space: nowrap;">
                                <?php echo e($stepLabel); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/multistep/step-indicator.blade.php ENDPATH**/ ?>