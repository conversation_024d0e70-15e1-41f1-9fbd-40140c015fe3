<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateMultiStepForm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:stepform
                            {name : The name of the form (e.g., UserForm, ProductForm)}
                            {--steps= : Number of steps for the form}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a multi-step Livewire form with configurable steps using reusable components';

    /**
     * INTERACTIVE MULTI-STEP FORM GENERATOR
     *
     * This command generates multi-step Livewire forms with an interactive setup process.
     *
     * USAGE:
     * php artisan make:multistep-form FormName --steps=4
     *
     * The command will then interactively ask for:
     * - Step 1 label name: [user input]
     * - Step 2 label name: [user input]
     * - Step 3 label name: [user input]
     * - Step 4 label name: [user input]
     * - Folder name (within livewire directory): [user input, defaults to kebab-case form name]
     *
     * REUSABLE COMPONENTS CREATED:
     *
     * This command uses reusable Blade components to avoid code duplication:
     *
     * 1. x-multistep.form-wrapper - Complete wrapper with step indicator and navigation
     *    Props: steps, currentStep, totalSteps, editMode, gap, showStepIndicator,
     *           showNavigation, prevButtonText, nextButtonText, submitButtonText,
     *           prevButtonClass, nextButtonClass, submitButtonClass
     *
     * 2. x-multistep.step-indicator - Just the step progress indicator
     *    Props: steps, currentStep, editMode, gap
     *
     * 3. x-multistep.navigation - Just the navigation buttons
     *    Props: currentStep, totalSteps, editMode, prevButtonText, nextButtonText,
     *           submitButtonText, prevButtonClass, nextButtonClass, submitButtonClass, showInCard
     *
     * EXAMPLE GENERATED USAGE:
     *
     * <x-multistep.form-wrapper
     *     :steps="$steps"
     *     :currentStep="$step"
     *     :totalSteps="$totalSteps"
     *     :editMode="$editMode"
     * >
     *     @if($step === 1)
     *         @include('livewire.folder.form-steps.step1')
     *     @elseif($step === 2)
     *         @include('livewire.folder.form-steps.step2')
     *     @endif
     * </x-multistep.form-wrapper>
     */

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $name = $this->argument('name');
        $steps = (int) $this->option('steps');

        // Validate steps input
        if (!$steps) {
            $this->error('Please provide the number of steps using --steps option');
            $this->line('Example: php artisan make:multistep-form PatientForm --steps=4');
            return 1;
        }

        if ($steps < 1) {
            $this->error('Number of steps must be at least 1');
            return 1;
        }

        if ($steps > 10) {
            $this->error('Number of steps cannot exceed 10 for practical reasons');
            return 1;
        }

        // Interactive prompts for step labels
        $this->info("Setting up {$steps} steps for '{$name}' form...");
        $this->line('');

        $labels = [];
        for ($i = 1; $i <= $steps; $i++) {
            $label = $this->ask("Step {$i} label name");
            if (empty($label)) {
                $this->error("Step label cannot be empty. Please provide a label for step {$i}.");
                $i--; // Retry the same step
                continue;
            }
            $labels[] = $label;
        }

        // Interactive prompt for folder name
        $this->line('');
        $folder = $this->ask('Folder name (within livewire directory)', Str::kebab($name));
        if (empty($folder)) {
            $folder = Str::kebab($name);
        }

        $namespace = 'App\\Http\\Livewire';

        // Generate files
        $this->generateLivewireComponent($name, $steps, $labels, $namespace, $folder);
        $this->generateParentBladeTemplate($name, $steps, $folder);
        $this->generateStepBladeFiles($name, $steps, $labels, $folder);

        $this->line('');
        $this->info("Multi-step form '{$name}' generated successfully!");
        $this->info("Generated files:");
        $this->line("- Livewire Component: " . $this->getComponentPath($name, $namespace, $folder));
        $this->line("- Parent Blade: " . $this->getParentBladePath($name, $folder));

        for ($i = 1; $i <= $steps; $i++) {
            $this->line("- Step {$i} Blade: " . $this->getStepBladePath($name, $i, $folder));
        }

        return 0;
    }



    /**
     * Generate the Livewire component class
     */
    private function generateLivewireComponent($name, $steps, $labels, $namespace, $folder)
    {
        $className = Str::studly($name);
        $kebabName = Str::kebab($name);

        $componentPath = $this->getComponentPath($name, $namespace, $folder);

        // Create directory if it doesn't exist
        $directory = dirname($componentPath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $stepsArray = $this->generateStepsArray($labels);
        $validationCases = $this->generateValidationCases($steps);

        $componentContent = $this->getComponentTemplate($className, $kebabName, $steps, $stepsArray, $validationCases, $namespace, $folder);

        File::put($componentPath, $componentContent);
    }

    /**
     * Generate the parent blade template
     */
    private function generateParentBladeTemplate($name, $steps, $folder)
    {
        $bladePath = $this->getParentBladePath($name, $folder);

        // Create directory if it doesn't exist
        $directory = dirname($bladePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        $stepIncludes = $this->generateStepIncludes($folder, $steps, $name);
        $bladeContent = $this->getParentBladeTemplate($stepIncludes);

        File::put($bladePath, $bladeContent);
    }

    /**
     * Generate individual step blade files
     */
    private function generateStepBladeFiles($name, $steps, $labels, $folder)
    {
        for ($i = 1; $i <= $steps; $i++) {
            $stepPath = $this->getStepBladePath($name, $i, $folder);

            // Create directory if it doesn't exist
            $directory = dirname($stepPath);
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
            }

            $stepContent = $this->getStepBladeTemplate($i, $labels[$i - 1]);
            File::put($stepPath, $stepContent);
        }
    }

    /**
     * Get component file path
     */
    private function getComponentPath($name, $namespace, $folder)
    {
        $className = Str::studly($name);
        $namespacePath = str_replace(['App\\', '\\'], ['app/', '/'], $namespace);
        return base_path("{$namespacePath}/{$folder}/{$className}.php");
    }

    /**
     * Get parent blade file path
     */
    private function getParentBladePath($name, $folder)
    {
        $kebabName = Str::kebab($name);
        return resource_path("views/livewire/{$folder}/{$kebabName}.blade.php");
    }

    /**
     * Get step blade file path
     */
    private function getStepBladePath($name, $step, $folder)
    {
        $kebabName = Str::kebab($name);
        return resource_path("views/livewire/{$folder}/{$kebabName}-steps/step{$step}.blade.php");
    }

    /**
     * Generate steps array for component
     */
    private function generateStepsArray($labels)
    {
        $stepsArray = [];
        foreach ($labels as $index => $label) {
            $stepsArray[] = ($index + 1) . " => '{$label}'";
        }
        return implode(",\n                ", $stepsArray);
    }

    /**
     * Generate validation cases for component
     */
    private function generateValidationCases($steps)
    {
        $cases = [];
        for ($i = 1; $i <= $steps; $i++) {
            $cases[] = "            case {$i}:\n                \$rules = [\n                    // Add validation rules for step {$i}\n                ];\n                break;";
        }
        return implode("\n\n", $cases);
    }

    /**
     * Generate step includes for blade template
     */
    private function generateStepIncludes($folder, $steps, $formName = null)
    {
        // Use the form name for the steps folder, not the folder name
        $formKebab = $formName ? Str::kebab($formName) : Str::kebab($folder);
        $includes = [];
        for ($i = 1; $i <= $steps; $i++) {
            if ($i === 1) {
                $includes[] = "@if(\$step === {$i})\n                    @include('livewire.{$folder}.{$formKebab}-steps.step{$i}')";
            } else {
                $includes[] = "@elseif(\$step === {$i})\n                    @include('livewire.{$folder}.{$formKebab}-steps.step{$i}')";
            }
        }
        $includes[] = "@endif";
        return implode("\n                ", $includes);
    }

    /**
     * Get the Livewire component template
     */
    private function getComponentTemplate($className, $kebabName, $steps, $stepsArray, $validationCases, $namespace, $folder)
    {
        return "<?php

namespace {$namespace};

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;

class {$className} extends Component
{
    use WithFileUploads;

    public \$step = 1;
    public \$editMode = false;
    public \$model;

    // Add your form fields here
    // public \$field1, \$field2, \$field3;

    // Dynamic steps configuration
    public \$steps = [];
    public \$totalSteps = {$steps};

    public function mount(\$model = null, \$editMode = false, \$steps = null)
    {
        \$this->editMode = \$editMode;
        \$this->initializeSteps(\$steps);

        if (\$this->editMode && \$model) {
            \$this->model = \$model;
            \$this->loadModelData();
        }
    }

    public function initializeSteps(\$customSteps = null)
    {
        if (\$customSteps) {
            \$this->steps = \$customSteps;
            \$this->totalSteps = count(\$customSteps);
        } else {
            // Default steps
            \$this->steps = [
                {$stepsArray}
            ];
            \$this->totalSteps = {$steps};
        }
    }

    public function loadModelData()
    {
        // Load model data into form fields
        // Example:
        // \$this->field1 = \$this->model->field1;
        // \$this->field2 = \$this->model->field2;
    }

    public function render()
    {
        return view('livewire.{$folder}.{$kebabName}');
    }

    public function nextStep()
    {
        \$this->validateCurrentStep();
        if (\$this->step < \$this->totalSteps) {
            \$this->step++;
        }
    }

    public function prevStep()
    {
        if (\$this->step > 1) {
            \$this->step--;
        }
    }

    public function goToStep(\$stepNumber)
    {
        // Only allow direct step navigation in edit mode
        if (\$this->editMode && \$stepNumber >= 1 && \$stepNumber <= \$this->totalSteps) {
            \$this->step = \$stepNumber;
        }
    }

    private function validateCurrentStep()
    {
        \$rules = [];

        switch (\$this->step) {
{$validationCases}
        }

        if (!empty(\$rules)) {
            \$this->validate(\$rules);
        }
    }

    public function submit()
    {
        \$this->validateCurrentStep();

        try {
            if (\$this->editMode) {
                \$this->updateModel();
            } else {
                \$this->createModel();
            }

            session()->flash('success-message', \$this->editMode ? 'Record updated successfully!' : 'Record created successfully!');
            // return redirect()->route('your.index.route');
        } catch (\\Exception \$e) {
            Log::error('Form save error: ' . \$e->getMessage());
            session()->flash('error-message', 'An error occurred while saving. Please try again.');
        }
    }

    private function createModel()
    {
        // Implement model creation logic
        // Example:
        // YourModel::create([
        //     'field1' => \$this->field1,
        //     'field2' => \$this->field2,
        // ]);
    }

    private function updateModel()
    {
        // Implement model update logic
        // Example:
        // \$this->model->update([
        //     'field1' => \$this->field1,
        //     'field2' => \$this->field2,
        // ]);
    }
}
";
    }

    /**
     * Get the parent blade template
     */
    private function getParentBladeTemplate($stepIncludes)
    {
        return '<x-multistep.form-wrapper
    :steps="$steps"
    :currentStep="$step"
    :totalSteps="$totalSteps"
    :editMode="$editMode"
>
    ' . $stepIncludes . '
</x-multistep.form-wrapper>
';
    }

    /**
     * Get the step blade template
     */
    private function getStepBladeTemplate($stepNumber, $stepLabel)
    {
        return "<div>
    <!-- Step {$stepNumber}: {$stepLabel} -->
    <!-- Add your form fields here -->

    {{-- Example form fields:
    <x-form.input.text label=\"Field 1\" labelRequired=\"1\" model=\"field1\" placeholder=\"Enter field 1\" />
    <x-form.input.text label=\"Field 2\" labelRequired=\"0\" model=\"field2\" placeholder=\"Enter field 2\" />
    --}}

    <div class=\"alert alert-info\">
        <strong>Step {$stepNumber}: {$stepLabel}</strong><br>
        Add your form fields for this step here.
    </div>
</div>
";
    }
}
