@props([
    'currentStep' => 1,
    'totalSteps' => 1,
    'editMode' => false,
    'prevButtonText' => 'Previous',
    'nextButtonText' => 'Next',
    'submitButtonText' => null,
    'prevButtonClass' => 'btn btn-secondary',
    'nextButtonClass' => 'btn btn-primary',
    'submitButtonClass' => 'btn btn-success',
    'showInCard' => true
])

@if($showInCard)
    <x-card.footer>
        <div class="d-flex justify-content-between">
            @if($currentStep > 1)
                <button type="button" class="{{ $prevButtonClass }}" wire:click="prevStep" wire:loading.attr="disabled">
                    <i class="fas fa-arrow-left me-1"></i> {{ $prevButtonText }}
                </button>
            @else
                <div></div>
            @endif

            @if($currentStep < $totalSteps)
                <button type="button" class="{{ $nextButtonClass }}" wire:click="nextStep" wire:loading.attr="disabled">
                    {{ $nextButtonText }} <i class="fas fa-arrow-right ms-1"></i>
                </button>
            @else
                <button type="button" class="{{ $submitButtonClass }}" wire:click="submit" wire:loading.attr="disabled" wire:loading.class="disabled">
                    <span wire:loading.remove wire:target="submit">
                        <i class="fas fa-save me-1"></i> {{ $submitButtonText ?? ($editMode ? 'Update' : 'Create') }}
                    </span>
                    <span wire:loading wire:target="submit">
                        <i class="fas fa-spinner fa-spin me-1"></i> {{ $editMode ? 'Updating...' : 'Creating...' }}
                    </span>
                </button>
            @endif
        </div>
    </x-card.footer>
@else
    <div class="d-flex justify-content-between">
        @if($currentStep > 1)
            <button type="button" class="{{ $prevButtonClass }}" wire:click="prevStep" wire:loading.attr="disabled">
                <i class="fas fa-arrow-left me-1"></i> {{ $prevButtonText }}
            </button>
        @else
            <div></div>
        @endif

        @if($currentStep < $totalSteps)
            <button type="button" class="{{ $nextButtonClass }}" wire:click="nextStep" wire:loading.attr="disabled">
                {{ $nextButtonText }} <i class="fas fa-arrow-right ms-1"></i>
            </button>
        @else
            <button type="button" class="{{ $submitButtonClass }}" wire:click="submit" wire:loading.attr="disabled" wire:loading.class="disabled">
                <span wire:loading.remove wire:target="submit">
                    <i class="fas fa-save me-1"></i> {{ $submitButtonText ?? ($editMode ? 'Update' : 'Create') }}
                </span>
                <span wire:loading wire:target="submit">
                    <i class="fas fa-spinner fa-spin me-1"></i> {{ $editMode ? 'Updating...' : 'Creating...' }}
                </span>
            </button>
        @endif
    </div>
@endif
