<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;

class ProviderController extends Controller
{
    /**
     * Show the form for creating a new provider.
     */
    public function create()
    {
        $has_back = route('users.index');
        $page_title = 'Create New Provider';

        $livewire_component = 'provider.provider-form';


        $provider = new User();
        $livewire_data = [
            'provider' => $provider,
            'editMode' => false,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    /**
     * Show the form for editing the specified provider.
     */
    public function edit(User $provider)
    {
        $has_back = route('users.index');
        $page_title = 'Edit Provider';

        $livewire_component = 'provider.provider-form';

        // Get custom steps configuration (can be different from create if needed)
        $customSteps = $this->getProviderSteps();

        $livewire_data = [
            'provider' => $provider,
            'editMode' => true,
            'steps' => $customSteps
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    /**
     * Get custom steps configuration for the provider form.
     * You can modify this method to return different steps based on your needs.
     */
    private function getProviderSteps()
    {
        return [
            1 => 'Basic Information',
            2 => 'Identification',
            3 => 'Contact and Address',
            4 => 'Signature Upload'
        ];
    }
}
