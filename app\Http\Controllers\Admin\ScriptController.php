<?php

namespace App\Http\Controllers\Admin;

use App\Events\ScriptStatusChanged;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Requests\VoidScriptRequest;
use App\Http\Resources\DataTableCollection;
use App\Jobs\SendDispenseJob;
use App\Jobs\SendFaxJob;
use App\Jobs\SendFilesToFaxJob;
use App\Jobs\VoidScriptJob;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\Medication;
use App\Models\State;
use App\Models\User;
use App\Models\WebHook;
use App\Services\LogService;
use App\Traits\FaxManager;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use App\Traits\DispenseProManager;


class ScriptController extends Controller
{
    use FaxManager, DispenseProManager;

    public function all()
    {
        return $this->renderScriptView('All scripts', 'scripts.index-all');
    }

    public function readyToSign()
    {
        return $this->renderScriptView('Ready to sign', 'scripts.index-ready-to-sign');

        // return $this->renderScriptView('Ready to Sign', Auth::user()->role === User::ROLE_PROVIDER ? 'scripts.index-ready-to-sign' : 'scripts.admin.index-ready-to-sign');
    }

    public function readyToSend()
    {
        return $this->renderScriptView('Ready to Send', 'scripts.index-ready-to-send');
    }

    public function sent()
    {
        return $this->renderScriptView('Sent', 'scripts.index-sent');
    }

    public function pendingApproval()
    {
        return $this->renderScriptView('Ready to send scripts for approval', 'scripts.index-pending-approval', false);
    }

    public function providerPendingApproval()
    {
        return $this->renderScriptView('Pending Approval', 'scripts.index-provider-pending-approval', false);
    }

    public function webhook(ImportFile $id)
    {
        $page_title = 'Webhook';

        $import_file = $id;
        return view('webhook.index', compact('import_file', 'page_title'));
    }

    public function webhookView(WebHook $webhook)
    {
        return response()->json([
            'data' => $webhook,
            'status' => 1
        ]);
    }

    private function renderScriptView(string $title, string $view, bool $showProviders = true, bool $showMedication = true)
    {
        $data = [
            'page_title' => $title,
        ];

        // if ($showProviders) {
        //     $data['providers'] = User::where('role', User::ROLE_PROVIDER)->get();
        // }
        if ($showProviders) {
            $data['providers'] = User::where('role', User::ROLE_PROVIDER)->get();
            $data['clinic_names'] = User::where('role', User::ROLE_PROVIDER)
                ->whereNotNull('clinic_name')
                ->pluck('clinic_name')
                ->unique()
                ->values();
        }

        if ($showMedication) {
            $data['medications'] = Medication::where('is_active', true)->get();
        }

        return view($view, $data);
    }

    public function preview(Request $request)
    {
        // Get the uploaded file
        if ($request->importId !== null) {
            $importFile = ImportFile::find($request->importId);

            if ($importFile) {
                // Check if file exists on public disk first, then try default disk
                if (Storage::disk('public')->exists($importFile->file_path)) {
                    return response()->file(storage_path('app/public/' . $importFile->file_path));
                } elseif (Storage::exists($importFile->file_path)) {
                    return Storage::response($importFile->file_path);
                }
            }
        }

        $latestImport = Import::latest()->first();
        if ($latestImport) {
            $firstFile = $latestImport->files()->first();
            if ($firstFile) {
                // Check if file exists on public disk first, then try default disk
                if (Storage::disk('public')->exists($firstFile->file_path)) {
                    return response()->file(storage_path('app/public/' . $firstFile->file_path));
                } elseif (Storage::exists($firstFile->file_path)) {
                    return Storage::response($firstFile->file_path);
                }
            }
        }

        $allData = session('all_prescription_data', []);
        if (!empty($allData)) {
            $data = $allData[0] ?? [];

            $user = Auth::user();
            $userState = null;
            $doctorName = 'Dr. April';

            if ($user) {
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            $pdf = PDF::loadView('pdf.prescription', [
                'data' => $data,
                'isPdfDownload' => false,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => $doctorName,
                'isSigned' => true,
                'userSignature' => $user->signature ? asset('storage/' . $user->signature) : null,

            ])->setPaper('letter');

            // Stream the PDF in the browser
            return $pdf->stream('prescription_preview.pdf');
        }

        $message = __('messages.script_not_found');

        session()->flash('error-message', $message);

        return back();
    }

    public function indexWebAll(GetDatatableRequest $request)
    {
        $user = Auth::user();

        $data = ImportFile::with('import');

        // Apply date filter if provided
        $signedDate = $request->input('query.query.signed_date');
        if ($signedDate && !empty($signedDate)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $signedDate);
                $data->whereDate('signed_at', $date);
                Log::info('Filtering by signed date', ['date' => $signedDate]);
            } catch (\Exception $e) {
                Log::error('Invalid date format', ['date' => $signedDate, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        // Apply provider filter if provided
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {
            $data->whereHas('import', function ($query) use ($providerId) {
                $query->where('user_id', $providerId);
            });
            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }

        // Apply medication filter if provided
        $medicationId = $request->input('query.query.medication_id');
        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }

        // Apply clinic_name filter if provided
        $clinicName = $request->input('query.query.clinic_name');
        if ($clinicName && !empty($clinicName)) {
            $data->whereHas('import.user', function ($query) use ($clinicName) {
                $query->where('clinic_name', $clinicName);
            });
            Log::info('Filtering by clinic name', ['clinic_name' => $clinicName]);
        }

        // if ($user->role == User::ROLE_PROVIDER) {
        //     $data = $data->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        // }

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        //old code
        // if ($search) {
        //     $query_search = "%" . $search . "%";

        //     $data->where(function ($query) use ($query_search) {
        //         $query->orWhere('first_name', 'like', $query_search)
        //             ->orWhere('last_name', 'like', $query_search)
        //             ->orWhere('medication', 'like', $query_search)
        //             ->orWhere('status', 'like', $query_search)
        //             ->orWhereRaw("DATE_FORMAT(script_date, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(signed_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(sent_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(created_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereHas('import', function ($query) use ($query_search) {
        //                 $query->where('file_name', 'like', $query_search);
        //             })
        //             ->orWhereHas('import.user', function ($q) use ($query_search) {
        //                 $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search);
        //             });
        //     });
        // }

        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }

            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('status', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });

                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }

        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importFileColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                // Field exists in Import table
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                // Special case for provider_name which is a computed field
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else if ($sort_field === 'import_file_name') {
                // Special case for import_file_name which comes from the import relationship
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->orderBy('imports.file_name', $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }

        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )->through(function ($item) {
                $item->import_file_name = $item->import->file_name ?? null;
                // Add provider name
                $provider = $item->import->user ?? null;
                // $item->provider = $provider;
                $item->provider_name = $provider ? ($provider->first_name . ' ' . $provider->last_name) : 'N/A';

                return $item;
            })
        );
    }
    public function webhookAll(GetDatatableRequest $request, $id)
    {
        $data = WebHook::where('import_file_id', $id);


        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $query_search = '%' . $search . '%';
            $data = $data->where(function ($query) use ($query_search) {
                $query->where('order_id', 'like', $query_search)
                    ->orWhere('ip_address', 'like', $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $webhookColumns = Schema::getColumnListing((new WebHook())->table);

            if (in_array($sort_field, $webhookColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }

        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )
        );
    }

    // This is for provider
    public function indexWebReadyToSign(GetDatatableRequest $request)
    {
        $user = Auth::user();

        $data = ImportFile::with(['import', 'returnedByUser']);

        // Apply date filter if provided
        // Try multiple possible locations for the script_date parameter
        $scriptDate = $request->input('query.query.script_date') ??
            $request->input('query.script_date') ??
            $request->input('script_date');

        if ($scriptDate && !empty($scriptDate)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $scriptDate);
                $formattedDate = $date->format('Y-m-d');

                // Use a flexible approach to match the date
                $data->where(function ($query) use ($formattedDate) {
                    $query->whereRaw("DATE(script_date) = ?", [$formattedDate])
                        ->orWhereDate('script_date', $formattedDate);
                });
            } catch (\Exception $e) {
                // Invalid date format, ignore filter
            }
        }

        // Apply provider filter if provided
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {
            $data->whereHas('import', function ($query) use ($providerId) {
                $query->where('user_id', $providerId);
            });
            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }

        // Apply medication filter if provided
        $medicationId = $request->input('query.query.medication_id');
        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }
        // Apply clinic_name filter if provided
        $clinicName = $request->input('query.query.clinic_name');
        if ($clinicName && !empty($clinicName)) {
            $data->whereHas('import.user', function ($query) use ($clinicName) {
                $query->where('clinic_name', $clinicName);
            });
            Log::info('Filtering by clinic name', ['clinic_name' => $clinicName]);
        }

        if ($user->role == User::ROLE_PROVIDER) {
            $data = $data->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        }

        $data = $data->whereIn('status', [ImportFile::STATUS_NEW, ImportFile::STATUS_PENDING_REVISION]);

        // Get selected IDs from request (if any)
        $selectedIds = $request->input('displayed_ids', []);

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }

            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });

                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }

        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importFileColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                // Field exists in Import table
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                // Special case for provider_name which is a computed field
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }


        $result = $data->paginate(
            $request->pagination['perpage'],
            ['*'],
            'page',
            $request->pagination['page']
        )->through(function ($item) use ($selectedIds) {
            $item->import_file_name = $item->import->file_name ?? null;
            // Mark the item as selected if it's in the selectedIds array
            $item->is_selected = in_array($item->id, $selectedIds);

            $provider = $item->import->user ?? null;
            $item->provider_name = $provider ? ($provider->first_name . ' ' . $provider->last_name) : 'N/A';

            // Add operator information to comment if available
            if ($item->comment && $item->returnedByUser) {
                $operator = $item->returnedByUser;
                // Use HTML with explicit line breaks and spacing for tooltip display
                $operatorInfo = "<br><br><div style='text-align: right; border-top: 1px solid #ddd; padding-top: 10px;'><div style='margin-bottom: 5px;'>{$operator->first_name} {$operator->last_name}</div><div>{$operator->email}</div></div>";
                $item->comment_with_operator = "<div style='text-align: left;'>{$item->comment}</div>" . $operatorInfo;
            } else {
                $item->comment_with_operator = $item->comment;
            }

            return $item;
        });

        // Add the selected IDs to the response meta data
        $result->additional = [
            'selectedIds' => $selectedIds
        ];

        return new DataTableCollection($result);
    }

    // This is for provider and some case for admin and operator
    public function indexWebReadyToSend(GetDatatableRequest $request)
    {
        $user = Auth::user();

        $data = ImportFile::with('import');

        if ($user->role === User::ROLE_PROVIDER) {
            $data = $data->where('status', ImportFile::STATUS_NEW);
        } else {
            $data = $data->whereIn('status', [
                // ImportFile::STATUS_PENDING_DISPATCH,
                ImportFile::STATUS_PENDING_APPROVAL
            ]);
        }

        // Apply date filter if provided
        $signedDate = $request->input('query.query.signed_date');
        if ($signedDate && !empty($signedDate)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $signedDate);
                $data->whereDate('signed_at', $date);
                Log::info('Filtering by signed date', ['date' => $signedDate]);
            } catch (\Exception $e) {
                Log::error('Invalid date format', ['date' => $signedDate, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        // Apply provider filter if provided
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {
            $data->whereHas('import', function ($query) use ($providerId) {
                $query->where('user_id', $providerId);
            });
            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }

        // Apply medication filter if provided
        $medicationId = $request->input('query.query.medication_id');
        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }
        // Apply clinic_name filter if provided
        $clinicName = $request->input('query.query.clinic_name');
        if ($clinicName && !empty($clinicName)) {
            $data->whereHas('import.user', function ($query) use ($clinicName) {
                $query->where('clinic_name', $clinicName);
            });
            Log::info('Filtering by clinic name', ['clinic_name' => $clinicName]);
        }


        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        //old code

        // if ($search) {
        //     $query_search = "%" . $search . "%";

        //     // Try to parse the search term as a date
        //     $dateSearch = null;

        //     $data->where(function ($q) use ($query_search, $search, $dateSearch) {

        //         // Search in other fields
        //         $q->where('first_name', 'like', $query_search)
        //             ->orWhere('last_name', 'like', $query_search)
        //             ->orWhere('medication', 'like', $query_search)
        //             ->orWhere('status', 'like', $query_search)
        //             ->orWhereRaw("DATE_FORMAT(script_date, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(signed_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(sent_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(created_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereHas('import', function ($query) use ($query_search) {
        //                 $query->where('file_name', 'like', $query_search);
        //             })
        //             ->orWhereHas('import.user', function ($q) use ($query_search) {
        //                 $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search);
        //             });
        //     });
        // }

        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }

            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });

                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }


        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importFileColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                // Field exists in Import table
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                // Special case for provider_name which is a computed field
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )->through(function ($item) {
                $item->import_file_name = $item->import->file_name ?? null;

                // Add provider name
                $provider = $item->import->user ?? null;
                $item->provider = $provider;
                $item->provider_name = $provider ? ($provider->first_name . ' ' . $provider->last_name) : 'N/A';

                return $item;
            })
        );
    }
    public function indexProviderPendingApproval(GetDatatableRequest $request)
    {
        $user = Auth::user();

        $data = ImportFile::with('import');

        // Apply date filter if provided
        // Try multiple possible locations for the script_date parameter
        $scriptDate = $request->input('query.query.script_date') ??
            $request->input('query.script_date') ??
            $request->input('script_date');

        if ($scriptDate && !empty($scriptDate)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $scriptDate);
                $formattedDate = $date->format('Y-m-d');

                // Use a flexible approach to match the date
                $data->where(function ($query) use ($formattedDate) {
                    $query->whereRaw("DATE(script_date) = ?", [$formattedDate])
                        ->orWhereDate('script_date', $formattedDate);
                });
            } catch (\Exception $e) {
                // Invalid date format, ignore filter
            }
        }

        // Apply provider filter if provided
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {
            $data->whereHas('import', function ($query) use ($providerId) {
                $query->where('user_id', $providerId);
            });
            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }

        // Apply medication filter if provided
        $medicationId = $request->input('query.query.medication_id');
        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }

        if ($user->role === User::ROLE_PROVIDER) {
            $data = $data->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        }

        $data = $data->where('status', ImportFile::STATUS_PENDING_APPROVAL);

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        // if ($search) {
        //     $query_search = "%" . $search . "%";

        //     // Try to parse the search term as a date
        //     $dateSearch = null;

        //     $data->where(function ($q) use ($query_search, $search, $dateSearch) {

        //         // Search in other fields
        //         $q->where('first_name', 'like', $query_search)
        //             ->orWhere('last_name', 'like', $query_search)
        //             ->orWhere('medication', 'like', $query_search)
        //             ->orWhere('status', 'like', $query_search)
        //             ->orWhereRaw("DATE_FORMAT(script_date, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(signed_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(sent_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(created_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereHas('import', function ($query) use ($query_search) {
        //                 $query->where('file_name', 'like', $query_search);
        //             })
        //             ->orWhereHas('import.user', function ($q) use ($query_search) {
        //                 $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search);
        //             });
        //     });
        // }

        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }

            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });

                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }

        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importFileColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                // Field exists in Import table
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                // Special case for provider_name which is a computed field
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }

        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )->through(function ($item) {
                $item->import_file_name = $item->import->file_name ?? null;
                return $item;
            })
        );
    }

    public function indexPendingApproval(GetDatatableRequest $request)
    {
        $user = Auth::user();

        $data = ImportFile::with('import');

        // if ($user->role !== User::ROLE_ADMIN) {
        //     $data = $data->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        // }

        $data = $data->where('status', ImportFile::STATUS_PENDING_APPROVAL);

        // Apply medication filter if provided
        $medicationId = $request->input('query.query.medication_id') ??
            $request->input('query.medication_id') ??
            $request->input('medication_id');

        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        // if ($search) {
        //     $query_search = "%" . $search . "%";

        //     $data->where(function ($query) use ($query_search) {
        //         $query->where('file_name', 'like', $query_search)
        //             ->orWhere('first_name', 'like', $query_search)
        //             ->orWhere('last_name', 'like', $query_search)
        //             ->orWhere('medication', 'like', $query_search);
        //     });
        // }

        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }

            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });

                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }

        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importFileColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                // Field exists in Import table
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                // Special case for provider_name which is a computed field
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }

        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )->through(function ($item) {
                $item->import_file_name = $item->import->file_name ?? null;
                return $item;
            })
        );
    }

    public function indexSent(GetDatatableRequest $request)
    {
        $user = Auth::user();
        $data = ImportFile::with('import');

        if ($user->role === User::ROLE_PROVIDER) {
            $data = $data->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        }

        $data = $data->where('status', ImportFile::STATUS_SENT);

        // Apply date filter if provided
        $sentDate = $request->input('query.query.sent_date');
        if ($sentDate && !empty($sentDate)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $sentDate);
                $data->whereDate('sent_at', $date);
                Log::info('Filtering by sent date', ['date' => $sentDate]);
            } catch (\Exception $e) {
                Log::error('Invalid date format', ['date' => $sentDate, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        // Apply provider filter if provided
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {
            $data->whereHas('import', function ($query) use ($providerId) {
                $query->where('user_id', $providerId);
            });
            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }

        // Apply medication filter if provided
        $medicationId = $request->input('query.query.medication_id');
        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }

        // Apply clinic_name filter if provided
        $clinicName = $request->input('query.query.clinic_name');
        if ($clinicName && !empty($clinicName)) {
            $data->whereHas('import.user', function ($query) use ($clinicName) {
                $query->where('clinic_name', $clinicName);
            });
            Log::info('Filtering by clinic name', ['clinic_name' => $clinicName]);
        }

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        // if ($search) {
        //     $query_search = "%" . $search . "%";

        //     // Try to parse the search term as a date
        //     $dateSearch = null;

        //     $data->where(function ($q) use ($query_search, $search, $dateSearch) {

        //         // Search in other fields
        //         $q->where('first_name', 'like', $query_search)
        //             ->orWhere('last_name', 'like', $query_search)
        //             ->orWhere('medication', 'like', $query_search)
        //             ->orWhereRaw("DATE_FORMAT(script_date, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereRaw("DATE_FORMAT(created_at, '%d/%m/%Y') LIKE ?", [$query_search])
        //             ->orWhereHas('import', function ($query) use ($query_search) {
        //                 $query->where('file_name', 'like', $query_search);
        //             })
        //             ->orWhereHas('import.user', function ($q) use ($query_search) {
        //                 $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search);
        //             });
        //     });
        // }
        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }

            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('order_id', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });

                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }

        if ($sort_order && $sort_field) {
            // Check if the sort field exists in ImportFile or Import table
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importFileColumns)) {
                // Field exists in ImportFile table
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                // Field exists in Import table
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                // Special case for provider_name which is a computed field
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }

        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )->through(function ($item) {
                $item->import_file_name = $item->import->file_name ?? null;
                // Add provider name
                $provider = $item->import->user ?? null;
                $item->provider_name = $provider ? ($provider->first_name . ' ' . $provider->last_name) : 'N/A';
                return $item;
            })
        );
    }

    public function downloadAllPdf(Request $request)
    {
        $user = Auth::user();
        // Determine the current status based on the request or route
        $currentRoute = request()->route()->getName();

        $query = ImportFile::with('import');

        if ($user->role === User::ROLE_PROVIDER) {
            $query = $query->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        }

        // Filter by patient_id if provided
        if ($request->has('patient_id') && !empty($request->patient_id)) {
            $query->where('patient_id', $request->patient_id);
        }

        if ($request->has('status')) {
            $status = $request->status;

            $query = $query->whereIn('status', $status);
        }
        // Get the IDs of records currently displayed in the table
        // This ensures we download exactly what's shown in the UI
        $displayedIds = request()->input('displayed_ids');
        $allWithStatus = request()->input('all_with_status');

        // Get the current query SQL for debugging
        $initialSql = $query->toSql();
        $initialBindings = $query->getBindings();

        // Count how many records would be returned without ID filtering
        $countBeforeIdFiltering = (clone $query)->count();

        // If we have specific IDs, use them (for "Download Selected")
        if ($displayedIds && is_array($displayedIds) && !empty($displayedIds)) {
            // Convert string IDs to integers if needed
            $numericIds = array_map(function ($id) {
                return is_numeric($id) ? (int)$id : $id;
            }, $displayedIds);

            // Only include the specific IDs provided
            $query->whereIn('id', $numericIds);

            // Get the updated query SQL for debugging
            $filteredSql = $query->toSql();
            $filteredBindings = $query->getBindings();
        } else {
            // If no specific IDs, apply filters for "Download All"

            // Apply sent date filter if provided
            if ($request->has('sent_date') && !empty($request->sent_date)) {
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $request->sent_date);
                    $query->whereDate('sent_at', $date);
                    Log::info('Filtering by sent date in downloadAllPdf', ['date' => $request->sent_date]);
                } catch (\Exception $e) {
                    Log::error('Invalid sent date format in downloadAllPdf', ['date' => $request->sent_date, 'error' => $e->getMessage()]);
                    // Invalid date format, ignore filter
                }
            }

            if ($request->has('signed_date') && !empty($request->signed_date)) {
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $request->signed_date);
                    $query->whereDate('signed_at', $date);
                    Log::info('Filtering by signed date in downloadAllPdf', ['date' => $request->signed_date]);
                } catch (\Exception $e) {
                    Log::error('Invalid signed date format in downloadAllPdf', ['date' => $request->signed_date, 'error' => $e->getMessage()]);
                    // Invalid date format, ignore filter
                }
            }
            if ($request->has('script_date') && !empty($request->script_date)) {
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $request->script_date);
                    $query->whereDate('script_date', $date);
                    Log::info('Filtering by script date in downloadAllPdf', ['date' => $request->script_date]);
                } catch (\Exception $e) {
                    Log::error('Invalid script date format in downloadAllPdf', ['date' => $request->script_date, 'error' => $e->getMessage()]);
                    // Invalid date format, ignore filter
                }
            }

            // Apply provider filter if provided
            if ($request->has('provider_id') && !empty($request->provider_id)) {
                $query->whereHas('import', function ($q) use ($request) {
                    $q->where('user_id', $request->provider_id);
                });
                Log::info('Filtering by provider in downloadAllPdf', ['provider_id' => $request->provider_id]);
            }

            // Apply medication filter if provided
            if ($request->has('medication_id') && !empty($request->medication_id)) {
                $medication = Medication::find($request->medication_id);
                if ($medication) {
                    $query->where('medication', $medication->name);
                    Log::info('Filtering by medication in downloadAllPdf', ['medication_id' => $request->medication_id, 'medication_name' => $medication->name]);
                }
            }

            // Apply clinic_name filter if provided
            if ($request->has('clinic_name') && !empty($request->clinic_name)) {
                $query->whereHas('import.user', function ($q) use ($request) {
                    $q->where('clinic_name', $request->clinic_name);
                });
                Log::info('Filtering by clinic name', ['clinic_name' => $request->clinic_name]);
            }

            //old code
            // // Apply search filter if provided
            // if ($request->has('search') && !empty($request->search)) {
            //     $searchTerm = '%' . $request->search . '%';
            //     $query->where(function ($q) use ($searchTerm) {
            //         $q->where('file_name', 'like', $searchTerm)
            //             ->orWhere('first_name', 'like', $searchTerm)
            //             ->orWhere('last_name', 'like', $searchTerm)
            //             ->orWhere('medication', 'like', $searchTerm);
            //     });
            //     Log::info('Filtering by search in downloadAllPdf', ['search' => $request->search]);
            // }

            // Apply search filter if provided
            if ($request->has('search') && !empty($request->search)) {
                $search = trim($request->search);
                $searchWords = preg_split('/\s+/', $search);
                $searchTerm = '%' . implode('%', $searchWords) . '%'; // e.g. "medical clinic" => "%medical%clinic%"

                $gender_match = null;
                $lower_search = strtolower($search);
                if (in_array($lower_search, ['m', 'male'])) {
                    $gender_match = 'M';
                } elseif (in_array($lower_search, ['f', 'female'])) {
                    $gender_match = 'F';
                }

                $query->where(function ($q) use ($searchTerm, $gender_match) {
                    $q->orWhere('file_name', 'like', $searchTerm)
                        ->orWhere('first_name', 'like', $searchTerm)
                        ->orWhere('last_name', 'like', $searchTerm)
                        ->orWhere('medication', 'like', $searchTerm)
                        ->orWhere('status', 'like', $searchTerm)
                        ->orWhere('dob', 'like', $searchTerm)
                        ->orWhere('address', 'like', $searchTerm)
                        ->orWhere('city', 'like', $searchTerm)
                        ->orWhere('state', 'like', $searchTerm)
                        ->orWhere('zip', 'like', $searchTerm)
                        ->orWhere('phone', 'like', $searchTerm)
                        ->orWhere('stregnth', 'like', $searchTerm)
                        ->orWhere('dosing', 'like', $searchTerm)
                        ->orWhere('refills', 'like', $searchTerm)
                        ->orWhere('vial_quantity', 'like', $searchTerm)
                        ->orWhere('sig', 'like', $searchTerm)
                        ->orWhere('notes', 'like', $searchTerm)
                        ->orWhere('comment', 'like', $searchTerm)
                        ->orWhere('number', 'like', $searchTerm)
                        ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$searchTerm])
                        ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$searchTerm])
                        ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$searchTerm])
                        ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$searchTerm])
                        ->orWhereHas('import', function ($importQuery) use ($searchTerm) {
                            $importQuery->where('file_name', 'like', $searchTerm);
                        })
                        ->orWhereHas('import.user', function ($userQuery) use ($searchTerm) {
                            $userQuery->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $searchTerm)
                                ->orWhere('email', 'like', $searchTerm)
                                ->orWhere('first_name', 'like', $searchTerm)
                                ->orWhere('last_name', 'like', $searchTerm)
                                ->orWhere('printed_name', 'like', $searchTerm)
                                ->orWhere('clinic_name', 'like', $searchTerm)
                                ->orWhere('NPI#', 'like', $searchTerm)
                                ->orWhere('LIC#', 'like', $searchTerm)
                                ->orWhere('DEA#', 'like', $searchTerm)
                                ->orWhere('phone', 'like', $searchTerm)
                                ->orWhere('fax', 'like', $searchTerm)
                                ->orWhere('address', 'like', $searchTerm)
                                ->orWhere('city', 'like', $searchTerm)
                                ->orWhere('zip', 'like', $searchTerm)
                                ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$searchTerm])
                                ->orWhereHas('state', function ($stateQ) use ($searchTerm) {
                                    $stateQ->where('name', 'like', $searchTerm);
                                });
                        });

                    if ($gender_match) {
                        $q->orWhere('gender', '=', $gender_match);
                    }
                });

                Log::info('Filtering by search in downloadAllPdf', ['search' => $request->search]);
            }
        }

        // Order by ID to ensure consistent ordering
        $query->orderBy('id', 'desc');

        $importFiles = $query->get();

        // Create a temporary directory to store PDFs
        $tempDir = storage_path('app/temp/prescriptions_' . time());
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // Track statistics for user feedback
        $totalFiles = count($importFiles);
        $signedFilesCount = 0;
        $addedToZipCount = 0;

        // Process each file and add to the temp directory
        foreach ($importFiles as $importFile) {
            // Check if the file has a status that requires signature
            $needsSignature = in_array($importFile->status, [
                // ImportFile::STATUS_SIGNED,
                ImportFile::STATUS_SENT,
                ImportFile::STATUS_PENDING_APPROVAL,
                ImportFile::STATUS_PENDING_REVISION,
                ImportFile::STATUS_VOIDED
            ]);

            // For files that need signature, ensure the signature is included in the PDF
            if ($needsSignature) {
                $signedFilesCount++;

                // Regenerate the PDF with signature to ensure it's included
                $this->regeneratePdfWithSignature($importFile);
            }

            $fileContent = null;

            // Check if file exists on public disk first, then try default disk
            if (Storage::disk('public')->exists($importFile->file_path)) {
                $fileContent = Storage::disk('public')->get($importFile->file_path);
            } elseif (Storage::exists($importFile->file_path)) {
                $fileContent = Storage::get($importFile->file_path);
            }

            if ($fileContent) {

                // Create a more descriptive filename based on patient data and status
                // Include the status in the filename to make it more informative
                $statusPrefix = '';
                switch ($importFile->status) {
                    // case ImportFile::STATUS_SIGNED:
                    //     $statusPrefix = 'Signed_';
                    //     break;
                    case ImportFile::STATUS_SENT:
                        $statusPrefix = 'Sent_';
                        break;
                    case ImportFile::STATUS_PENDING_APPROVAL:
                        $statusPrefix = 'PendingApproval_';
                        break;
                    case ImportFile::STATUS_PENDING_REVISION:
                        $statusPrefix = 'PendingRevision_';
                        break;
                    case ImportFile::STATUS_VOIDED:
                        $statusPrefix = 'Voided_';
                        break;
                    default:
                        $statusPrefix = '';
                }

                $firstName = isset($importFile->first_name)
                    ? substr(preg_replace('/\s+/', '', $importFile->first_name), 0, 5)
                    : '';

                $lastName = isset($importFile->last_name)
                    ? substr(preg_replace('/\s+/', '', $importFile->last_name), 0, 5)
                    : '';

                $fileName = $statusPrefix . 'prescription_' .
                    ($importFile->number ? 'row' . $importFile->number . '_' : '') .
                    $importFile->id . '_' .
                    $lastName . '_' .
                    $firstName . '.pdf';


                // Replace spaces with underscores and remove any special characters
                $fileName = str_replace(' ', '_', $fileName);
                $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                file_put_contents($tempDir . '/' . $fileName, $fileContent);
                $addedToZipCount++;
            }
        }

        // If no files were added to the zip, return with a message
        if ($addedToZipCount === 0) {
            // Clean up the temporary directory
            if (file_exists($tempDir)) {
                rmdir($tempDir);
            }
            $message = __('messages.no_script_found_to_download');

            session()->flash('error-message', $message);
            return back();
        }

        // Create a ZIP file
        $zipFileName = 'prescriptions_' . date('Y-m-d') . '.zip';
        $zipFilePath = storage_path('app/temp/' . $zipFileName);

        $zip = new \ZipArchive();
        if ($zip->open($zipFilePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === TRUE) {
            // Add files to the zip
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($tempDir),
                \RecursiveIteratorIterator::LEAVES_ONLY
            );

            foreach ($files as $file) {
                if (!$file->isDir()) {
                    $filePath = $file->getRealPath();
                    // Use a more unique naming scheme to prevent filename collisions
                    // Include the full path hash to ensure uniqueness
                    $pathHash = substr(md5($filePath), 0, 8);
                    $relativePath = $pathHash . '_' . basename($filePath);
                    $zip->addFile($filePath, $relativePath);
                }
            }

            $zip->close();

            // Clean up the temporary directory
            array_map('unlink', glob("$tempDir/*.*"));
            rmdir($tempDir);

            // Log statistics
            Log::info('Downloaded PDFs', [
                'total_files' => $totalFiles,
                'signed_and_sent_for_approval_files' => $signedFilesCount,
                'added_to_zip' => $addedToZipCount
            ]);

            // Log bulk download
            LogService::logScriptDownloaded([
                'patient_name' => 'Multiple scripts',
                'medication' => 'Multiple medications',
                'script_count' => $addedToZipCount,
                'total_files' => $totalFiles
            ], 'bulk');

            // Return the ZIP file for download
            return response()->download($zipFilePath, $zipFileName, [
                'Content-Type' => 'application/zip'
            ])->deleteFileAfterSend(true);
        }
        $message = __('messages.zip_create_error');

        session()->flash('error-message', $message);

        return back();
    }

    // This is for provider
    public function signAll(Request $request)
    {
        $user = Auth::user();

        $query = ImportFile::with('import');

        if ($user->role == User::ROLE_PROVIDER) {
            $query = $query->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        }

        // Determine if specific IDs are provided
        if ($request->has('displayed_ids') && is_array($request->displayed_ids)) {
            $import_files = $query
                ->whereIn('id', $request->displayed_ids)
                ->get();
        } else {
            if ($request->has('status')) {
                // If no specific IDs, apply status filter and other filters
                $query = $query->whereIn('status', $request->status);
            }

            // Apply medication filter if provided
            if ($request->has('medication_id') && !empty($request->medication_id)) {
                $medication = Medication::find($request->medication_id);
                if ($medication) {
                    $query->where('medication', $medication->name);
                    Log::info('Filtering by medication in signAll', ['medication_id' => $request->medication_id, 'medication_name' => $medication->name]);
                }
            }

            // Apply script date filter if provided
            if ($request->has('script_date') && !empty($request->script_date)) {
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $request->script_date);
                    $formattedDate = $date->format('Y-m-d');

                    // Use a flexible approach to match the date
                    $query->where(function ($q) use ($formattedDate) {
                        $q->whereRaw("DATE(script_date) = ?", [$formattedDate])
                            ->orWhereDate('script_date', $formattedDate);
                    });
                    Log::info('Filtering by script date in signAll', ['date' => $request->script_date]);
                } catch (\Exception $e) {
                    Log::error('Invalid date format in signAll', ['date' => $request->script_date, 'error' => $e->getMessage()]);
                    // Invalid date format, ignore filter
                }
            }

            // Apply search filter if provided
            if ($request->has('search') && !empty($request->search)) {
                $searchTerm = '%' . $request->search . '%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('file_name', 'like', $searchTerm)
                        ->orWhere('first_name', 'like', $searchTerm)
                        ->orWhere('last_name', 'like', $searchTerm)
                        ->orWhere('medication', 'like', $searchTerm);
                });
                Log::info('Filtering by search in signAll', ['search' => $request->search]);
            }

            $import_files = $query->get();
        }

        // Check if we have a client timestamp from the request
        $clientTimestamp = $request->input('client_timestamp');
        $signedAt = null;

        if ($clientTimestamp) {
            try {
                // Parse the timestamp with timezone information
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s', $clientTimestamp);
                Log::info('Using client timestamp for signed_at', [
                    'timestamp' => $clientTimestamp
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to parse client timestamp', [
                    'timestamp' => $clientTimestamp,
                    'error' => $e->getMessage()
                ]);
                // Fall through to try session timestamp or current time
                $this->tryUseSessionOrCurrentTime($signedAt);
            }
        } else {
            // No client timestamp in request, try session or current time
            $this->tryUseSessionOrCurrentTime($signedAt);
        }

        $script_pending_to_sign = 0;
        $script_sign = 0;
        foreach ($import_files as $import_file) {
            if ($import_file->is_eligible_for_signing) {
                $import_file->status = $request->changed_status;
                $import_file->signed_at = $signedAt;
                $import_file->save();

                // Log script signing
                LogService::logScriptSigned([
                    'patient_name' => $import_file->first_name . ' ' . $import_file->last_name,
                    'medication' => $import_file->medication,
                    'script_id' => $import_file->id,
                    'status' => $import_file->status
                ]);

                $script_sign++;
                // Regenerate the PDF with the updated signed_at timestamp
                $this->regeneratePdfWithSignature($import_file);
            } else {
                $script_pending_to_sign++;
            }
        }

        // If status is changed to PENDING_APPROVAL, dispatch the ScriptStatusChanged event
        if ($request->changed_status === ImportFile::STATUS_PENDING_APPROVAL && !empty($import_files)) {
            // Convert collection to array for the event
            $updatedFiles = $import_files->toArray();

            // Dispatch the ScriptStatusChanged event if there are updated files
            if (!empty($updatedFiles) && $user) {
                event(new ScriptStatusChanged($updatedFiles, $user));
                Log::info('ScriptStatusChanged event dispatched from signAll', [
                    'user_id' => $user->id,
                    'user_name' => $user->first_name . ' ' . $user->last_name,
                    'count' => count($updatedFiles)
                ]);
            }
        }

        if ($script_sign > 0) {
            $message = __('messages.script_signed_sent');
            session()->flash('success-message', $message);
        }

        if ($script_pending_to_sign > 0) {
            session()->flash('warning-message', 'Note: Some scripts were skipped due to pending revision.');
        }

        return redirect()->back();
    }

    public function queues(Request $request)
    {
        $page_title = 'Queue Progress';

        $livewire_component = 'scripts-panel';
        $livewire_data = [
            'page_title' => $page_title,
            'request' => $request
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    // This is for provider
    public function sendAllForApproval(Request $request)
    {
        $user = Auth::user();

        $query = ImportFile::with('import')->whereHas('import', fn($q) => $q->where('user_id', $user->id));

        // if ($user->role !== User::ROLE_ADMIN) {
        //     $query = $query->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        // }

        // Determine if specific IDs are provided
        if ($request->has('displayed_ids') && is_array($request->displayed_ids)) {
            $import_files = $query
                ->whereIn('id', $request->displayed_ids)
                ->get();
        } else {
            // If no specific IDs, apply status filter
            $import_files = $query
                ->where('status', $request->status)
                ->get();
        }

        $updatedFiles = [];
        foreach ($import_files as $import_file) {
            if ($import_file->status !== ImportFile::STATUS_NEW) {
                continue;
            }
            $import_file->status = $request->changed_status; // fallback to 'Pending Approval' if not passed
            $import_file->sent_at = Carbon::now();
            $import_file->save();

            // Add to updated files array for event dispatching
            $updatedFiles[] = $import_file->toArray();
        }

        // If status is changed to PENDING_APPROVAL, dispatch the ScriptStatusChanged event
        if ($request->changed_status === ImportFile::STATUS_PENDING_APPROVAL && !empty($updatedFiles)) {
            // Dispatch the ScriptStatusChanged event if there are updated files
            if (!empty($updatedFiles) && $user) {
                event(new ScriptStatusChanged($updatedFiles, $user));
                Log::info('ScriptStatusChanged event dispatched from sendAllForApproval', [
                    'user_id' => $user->id,
                    'user_name' => $user->first_name . ' ' . $user->last_name,
                    'count' => count($updatedFiles)
                ]);
            }
        }

        $message = __('messages.script_sent_for_approval');

        session()->flash('success-message', $message);

        return redirect()->back();
    }

    /**
     * Delete a script (ImportFile)
     * Only administrators can delete scripts with status 'New' or 'Pending Approval'
     */
    public function delete(ImportFile $importFile)
    {
        $user = Auth::user();

        // Check if user is administrator
        if ($user->role !== User::ROLE_ADMIN) {
            return response([
                'message' => __('messages.delete_script_unauthorized'),
                'status' => '0',
            ], 403);
        }

        // Check if script status allows deletion
        $allowedStatuses = [ImportFile::STATUS_NEW, ImportFile::STATUS_PENDING_APPROVAL];
        if (!in_array($importFile->status, $allowedStatuses)) {
            return response([
                'message' => __('messages.delete_script_error'),
                'status' => '0',
            ], 400);
        }

        try {
            // Log script deletion before deleting
            LogService::logScriptDeleted([
                'patient_name' => $importFile->first_name . ' ' . $importFile->last_name,
                'medication' => $importFile->medication,
                'script_id' => $importFile->id,
                'status' => $importFile->status
            ]);

            // Delete the import file
            $importFile->delete();

            return response([
                'message' => __('messages.script_deleted'),
                'status' => '1',
            ]);
        } catch (\Exception $e) {
            return response([
                'message' => __('messages.script_delete_error') . $e->getMessage(),
                'status' => '0',
            ], 500);
        }
    }

    /**
     * Helper method to try using device time from session or current server time
     *
     * @param Carbon|null &$signedAt Reference to the signedAt variable to update
     * @return void
     */
    private function tryUseSessionOrCurrentTime(&$signedAt)
    {
        // Try to get device time from session
        $deviceTime = session('device_time');

        if ($deviceTime) {
            try {
                // Parse the timestamp with timezone information
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s O', $deviceTime);
                Log::info('Using device time from session for signed_at', [
                    'timestamp' => $deviceTime
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to parse device time from session', [
                    'timestamp' => $deviceTime,
                    'error' => $e->getMessage()
                ]);
                // Fall through to use current time
                $signedAt = Carbon::now();
            }
        } else {
            // If no device time in session, use current server time
            $signedAt = Carbon::now();
            Log::info('Using current server time for signed_at');
        }
    }

    /**
     * Regenerate the PDF with signature and updated timestamp
     *
     * @param ImportFile $importFile The import file to regenerate PDF for
     * @return void
     */
    private function regeneratePdfWithSignature(ImportFile $importFile)
    {
        // Skip if file path is not set
        if (empty($importFile->file_path)) {
            Log::warning('Cannot regenerate PDF - file path is empty', [
                'import_file_id' => $importFile->id
            ]);
            return;
        }

        // Get user data for the PDF
        $user = User::find($importFile->import->user_id);
        $userState = null;
        $doctorName = 'Dr. April';

        if ($user) {
            $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
            if ($user->state_id) {
                $userState = State::find($user->state_id);
            }
        }

        // Prepare data array for PDF
        $data = [];
        // Format script_date in DD/MM/YYYY format
        $data[] = $importFile->script_date ? Carbon::parse($importFile->script_date)->format('m/d/Y') : '';
        $data[] = $importFile->last_name;
        $data[] = $importFile->first_name ?? '';
        // Format dob in DD/MM/YYYY format
        $data[] = $importFile->dob ? Carbon::parse($importFile->dob)->format('m/d/Y') : '';
        $data[] = $importFile->gender;
        $data[] = $importFile->address;
        $data[] = $importFile->city;
        $data[] = $importFile->state;
        $data[] = $importFile->zip;
        $data[] = $importFile->phone;
        $data[] = $importFile->medication;
        // $data[] = $importFile->stregnth;   //commented due to remova of 2 fields from import flow
        // $data[] = $importFile->dosing;     //commented due to remova of 2 fields from import flow
        $data[] = $importFile->refills;
        $data[] = $importFile->vial_quantity;
        $data[] = $importFile->days_supply;
        $data[] = $importFile->sig ?? '';
        $data[] = $importFile->notes ?? '';
        $data[] = $importFile->ship_to ?? '';

        // Get signature image path
        $signatureImagePath = null;
        if ($user && $user->signature) {
            // Convert storage path to absolute filesystem path
            $signatureImagePath = storage_path('app/public/' . $user->signature);

            // Ensure file exists
            if (!file_exists($signatureImagePath)) {
                $signatureImagePath = null;
            }
        }

        // Format the signed_at timestamp
        $formattedSignedAt = $importFile->signed_at ? Carbon::parse($importFile->signed_at)->format('m/d/Y h:i A') : now()->format('m/d/Y h:i A');

        // Log the timestamp for debugging
        Log::info('Regenerating PDF with signed_at timestamp', [
            'import_file_id' => $importFile->id,
            'signed_at_db' => $importFile->signed_at,
            'formatted_signed_at' => $formattedSignedAt
        ]);

        // Generate PDF
        $pdf = PDF::loadView('pdf.prescription', [
            'data' => $data,
            'isPdfDownload' => true,
            'user' => $user,
            'userState' => $userState,
            'doctorName' => $doctorName,
            'isSigned' => true,
            'userSignature' => $signatureImagePath,
            'signed_at' => $formattedSignedAt,
            'ip_address' => request()->ip(),
        ]);

        $pdf->setOption('isPhpEnabled', true);
        $pdf->setOption('isHtml5ParserEnabled', true);
        $pdf->setOption('isRemoteEnabled', false);
        $pdf->setPaper('letter');

        // Save the new PDF to the same path
        Storage::put($importFile->file_path, $pdf->output());
    }

    /**
     * Regenerate the PDF without signature for returned scripts
     *
     * @param ImportFile $importFile The import file to regenerate PDF for
     * @return void
     */
    private function regeneratePdfWithoutSignature(ImportFile $importFile)
    {
        try {
            // Skip if file path is not set
            if (empty($importFile->file_path)) {
                Log::warning('Cannot regenerate PDF - file path is empty', [
                    'import_file_id' => $importFile->id
                ]);
                return;
            }

            // Get user data for the PDF
            $user = User::find($importFile->import->user_id);
            $userState = null;
            $doctorName = 'Dr. April';

            if ($user) {
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            // Prepare data array for PDF
            $data = [];
            // Format script_date in DD/MM/YYYY format - handle potential non-date values
            try {
                $data[] = $importFile->script_date ? Carbon::parse($importFile->script_date)->format('m/d/Y') : '';
            } catch (\Exception $e) {
                $data[] = $importFile->script_date ?? '';
            }

            $data[] = $importFile->last_name ?? '';
            $data[] = $importFile->first_name ?? '';

            // Format dob in DD/MM/YYYY format - handle potential non-date values
            try {
                $data[] = $importFile->dob ? Carbon::parse($importFile->dob)->format('m/d/Y') : '';
            } catch (\Exception $e) {
                $data[] = $importFile->dob ?? '';
            }

            $data[] = $importFile->gender ?? '';
            $data[] = $importFile->address ?? '';
            $data[] = $importFile->city ?? '';
            $data[] = $importFile->state ?? '';
            $data[] = $importFile->zip ?? '';
            $data[] = $importFile->phone ?? '';
            $data[] = $importFile->medication ?? '';
            // $data[] = $importFile->stregnth ?? '';   //commented due to remova of 2 fields from import flow
            // $data[] = $importFile->dosing ?? '';     //commented due to remova of 2 fields from import flow
            $data[] = $importFile->refills ?? '';
            $data[] = $importFile->vial_quantity ?? '';
            $data[] = $importFile->days_supply ?? '';
            $data[] = $importFile->sig ?? '';
            $data[] = $importFile->notes ?? '';
            $data[] = $importFile->ship_to ?? '';

            // Log for debugging
            Log::info('Regenerating PDF without signature', [
                'import_file_id' => $importFile->id,
                'file_path' => $importFile->file_path,
                'isSigned' => false,
                'userSignature' => null,
                'signed_at' => null,
                'doctorName' => ''
            ]);

            // Delete the existing PDF file first to ensure fresh generation
            if (Storage::exists($importFile->file_path)) {
                Storage::delete($importFile->file_path);
                Log::info('Deleted existing PDF file', ['file_path' => $importFile->file_path]);
            }

            // Generate PDF without signature
            $pdf = PDF::loadView('pdf.prescription', [
                'data' => $data,
                'isPdfDownload' => false,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => '', // Remove doctor name for unsigned PDFs
                'isSigned' => false,
                'userSignature' => null,
                'signed_at' => null,
                'ip_address' => null, // Remove IP address for unsigned PDFs
            ]);

            $pdf->setOption('isPhpEnabled', true);
            $pdf->setOption('isHtml5ParserEnabled', true);
            $pdf->setOption('isRemoteEnabled', false);
            $pdf->setPaper('letter');

            // Save the new PDF to the same path
            Storage::put($importFile->file_path, $pdf->output());

            Log::info('Successfully regenerated PDF without signature', [
                'import_file_id' => $importFile->id,
                'file_path' => $importFile->file_path
            ]);
        } catch (\Exception $e) {
            Log::error('Error in regeneratePdfWithoutSignature function', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'import_file_id' => $importFile->id
            ]);
            // Don't throw the exception, just log it so the main function can continue
        }
    }

    public function sendFaxAll(Request $request)
    {
        // Log all request parameters for debugging
        Log::info('sendFaxAll request parameters', [
            'all_params' => $request->all(),
            'provider_id' => $request->provider_id,
            'has_provider_id' => $request->has('provider_id'),
            'medication_id' => $request->medication_id,
            'signed_date' => $request->signed_date,
            'displayed_ids' => $request->displayed_ids
        ]);

        $query = ImportFile::with('import')->where('status', ImportFile::STATUS_PENDING_APPROVAL);

        // Apply provider filter if provided
        if ($request->has('provider_id') && !empty($request->provider_id)) {
            Log::info('About to apply provider filter', ['provider_id' => $request->provider_id]);

            // Get the original SQL query for debugging
            $originalSql = $query->toSql();
            $originalBindings = $query->getBindings();
            Log::info('Original query before provider filter', ['sql' => $originalSql, 'bindings' => $originalBindings]);

            $query->whereHas('import', function ($q) use ($request) {
                $q->where('user_id', $request->provider_id);
            });

            // Get the updated SQL query for debugging
            $filteredSql = $query->toSql();
            $filteredBindings = $query->getBindings();
            Log::info('Query after provider filter', ['sql' => $filteredSql, 'bindings' => $filteredBindings]);

            Log::info('Filtering by provider in sendFaxAll', ['provider_id' => $request->provider_id]);
        } else {
            Log::warning('No provider filter applied', [
                'has_provider_id' => $request->has('provider_id'),
                'provider_id_value' => $request->provider_id
            ]);
        }

        // Apply medication filter if provided
        if ($request->has('medication_id') && !empty($request->medication_id)) {
            $medication = Medication::find($request->medication_id);
            if ($medication) {
                $query->where('medication', $medication->name);
                Log::info('Filtering by medication in sendFaxAll', ['medication_id' => $request->medication_id, 'medication_name' => $medication->name]);
            }
        }

        // Apply signed date filter if provided
        if ($request->has('signed_date') && !empty($request->signed_date)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $request->signed_date);
                $query->whereDate('signed_at', $date);
                Log::info('Filtering by signed date in sendFaxAll', ['date' => $request->signed_date]);
            } catch (\Exception $e) {
                Log::error('Invalid date format in sendFaxAll', ['date' => $request->signed_date, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('file_name', 'like', $searchTerm)
                    ->orWhere('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('medication', 'like', $searchTerm);
            });
            Log::info('Filtering by search in sendFaxAll', ['search' => $request->search]);
        }

        // Get the final SQL query for debugging
        $finalSql = $query->toSql();
        $finalBindings = $query->getBindings();
        Log::info('Final query before execution', ['sql' => $finalSql, 'bindings' => $finalBindings]);

        // Determine if specific IDs are provided
        if ($request->has('displayed_ids') && is_array($request->displayed_ids)) {
            // If we have specific IDs, we need to make sure they match our filters
            // First, get all IDs that match our filters
            $filteredIds = $query->pluck('id')->toArray();

            // Then, only use the displayed_ids that are also in our filtered set
            $validDisplayedIds = array_intersect($request->displayed_ids, $filteredIds);

            Log::info('Filtering displayed_ids', [
                'original_count' => count($request->displayed_ids),
                'filtered_count' => count($validDisplayedIds),
                'original_ids' => $request->displayed_ids,
                'filtered_ids' => $validDisplayedIds
            ]);

            // Now get the records using the filtered IDs
            $import_files = ImportFile::with('import')
                ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
                ->whereIn('id', $validDisplayedIds)
                ->get();

            Log::info('Using filtered displayed_ids', [
                'count' => count($validDisplayedIds),
                'results_count' => $import_files->count()
            ]);
        } else {
            // If no specific IDs, use the query with all our filters
            $import_files = $query->get();
            Log::info('No displayed_ids provided, using all matching records', [
                'results_count' => $import_files->count()
            ]);
        }

        // Update status to Pending Dispatch before dispatching the job
        foreach ($import_files as $import_file) {
            $import_file->status = ImportFile::STATUS_PENDING_DISPATCH;
            $import_file->save();

            // Log script sending
            LogService::logScriptSent([
                'patient_name' => $import_file->first_name . ' ' . $import_file->last_name,
                'medication' => $import_file->medication,
                'script_id' => $import_file->id,
                'status' => $import_file->status
            ]);
        }

        $import_file_ids = $import_files->pluck('id')->toArray();
        $import_file_fax = [];
        $import_file_dispense = [];

        foreach ($import_files as $import_file) {
            $provider = $import_file->import->user ?? null;
            if ($provider && $provider->default_dispatch_method === User::DISPATCH_METHOD_FAX) {
                $import_file_fax[] = $import_file->id;
            } elseif ($provider && $provider->default_dispatch_method === User::DISPATCH_METHOD_DISPENSE_PRO) {
                $import_file_dispense[] = $import_file->id;
            }
        }
        // Log the IDs being sent to the job
        Log::info('Sending IDs to SendFilesToFaxJob', [
            'import_file_fax' => $import_file_fax,
            'import_file_dispense' => $import_file_dispense,
            'count' => count($import_file_ids)
        ]);

        $total_files_for_dispense = count($import_file_dispense);

        $user = Auth::user();
        try {
            // if ($total_files_to_send >= 100) {
            //     $chunkSize = round($total_files_to_send / 4);
            //     $divided_ids = array_chunk($import_file_ids, $chunkSize);


            //     if (!empty($divided_ids)) {
            //         // SendFilesToFaxJob::dispatch($import_file_ids, $user);
            //         foreach ($divided_ids as $divided_id_array) {
            //             SendFilesToFaxJob::dispatch($divided_id_array, $user);
            //         }
            //         Log::info('SendFilesToFaxJob dispatched successfully', ['user_id' => $user ? $user->id : null]);
            //     } else {
            //         Log::warning('No import file IDs to process');
            //     }
            // } else {

            if (!empty($import_file_fax)) {
                SendFilesToFaxJob::dispatch($import_file_fax, $user, [
                    'total' => count($import_file_fax),
                    'title' => 'Sending Scripts to Fax Plus',
                ]);
                Log::info('SendFilesToFaxJob dispatched successfully', ['user_id' => $user ? $user->id : null]);
            } else {
                Log::warning('No import file IDs to process');
            }

            if ($total_files_for_dispense > 0) {
                // if ($total_files_for_dispense >= 100) {
                // $chunkSize = round($total_files_for_dispense / 4);
                $divided_ids = array_chunk($import_file_dispense, 30);

                if (!empty($divided_ids)) {
                    foreach ($divided_ids as $divided_id_array) {
                        SendDispenseJob::dispatch($divided_id_array, $user, [
                            'total' => count($divided_id_array),
                            'title' => 'Sending Scripts to Dispense Pro',
                        ]);
                    }
                    Log::info('SendDispenseJob dispatched successfully in chunks', ['user_id' => $user ? $user->id : null]);
                } else {
                    Log::warning('No import file IDs to process for dispense');
                }
                // } else {
                //     SendDispenseJob::dispatch($import_file_dispense, $user);
                //     Log::info('SendDispenseJob dispatched successfully', ['user_id' => $user ? $user->id : null]);
                // }
            } else {
                Log::warning('No import file IDs to process for dispense');
            }

            // }

            return redirect()->back()->with('success-message', "Script queued for dispatch. Status changed to 'Pending Dispatch' until delivery is confirmed.");
        } catch (\Throwable $th) {
            // If there's an error dispatching the job, revert status back to Pending Approval
            Log::error('Error dispatching SendFilesToFaxJob', [
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString()
            ]);

            foreach ($import_files as $import_file) {
                $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                $import_file->save();
            }
            return redirect()->back()->with('error', $th->getMessage());
        }



        //Moving this whole logic into queue
        // $filepathArray = [];

        // DB::beginTransaction();
        // foreach ($import_files as $import_file) {
        //     if ($import_file->status !== ImportFile::STATUS_PENDING_APPROVAL) {
        //         continue;
        //     }

        //     $file = $import_file->file_path;
        //     if (Storage::exists($file)) {
        //         $filepathArray[] = Storage::path($file);
        //     }

        //     // $import_file->status = $request->changed_status;
        //     // $import_file->save();
        // }

        // $uploadedFiles = $filepathArray;

        // if (!is_array($uploadedFiles)) {
        //     $uploadedFiles = [$uploadedFiles];
        // }

        // $filesArray = [];
        // foreach ($uploadedFiles as $file) {
        //     $response = $this->uploadFiles($file);

        //     if (isset($response['path'])) {
        //         $filesArray[] = $response['path'];
        //     }
        // }


        // try {
        //     if (!empty($filesArray)) {
        //         $userId = $request->input('user_id', config('fax.user_id'));
        //         $to = $request->input('to', config('fax.to_number'));
        //         $from = $request->input('from', config('fax.from_number'));

        //         try {
        //             $sendFax = $this->sendFax($to, $filesArray, $from, $userId);

        //             if ($sendFax) {
        //                 foreach ($import_files as $import_file) {
        //                     $import_file->status = ImportFile::STATUS_SENT;
        //                     $import_file->save();
        //                 }
        //                 Log::info('Fax sent successfully', [
        //                     'to' => $to,
        //                     'from' => $from,
        //                     'files' => $filesArray
        //                 ]);
        //             } else {
        //                 Log::error('Failed to send fax', [
        //                     'to' => $to,
        //                     'from' => $from,
        //                     'files' => $filesArray
        //                 ]);
        //                 $message = 'Failed to send fax. Please try again later.';

        //                 session()->flash('error-message', $message);

        //                 return redirect()->back();
        //             }
        //         } catch (\App\Exceptions\CustomException $e) {
        //             // Handle Fax API error response
        //             Log::error('Fax API Error', [
        //                 'message' => $e->getMessage(),
        //                 'code' => $e->getCode()
        //             ]);

        //             // Parse the error message if it's in JSON format
        //             $errorData = json_decode($e->getMessage(), true);
        //             $errorMessage = 'Fax API Error: ';

        //             if (is_array($errorData) && isset($errorData['description'])) {
        //                 // Format from the logs: {"description":"too_many_files","error":403}
        //                 $errorMessage .= $errorData['description'];
        //             } else {
        //                 $errorMessage .= $e->getMessage();
        //             }

        //             session()->flash('error-message', $errorMessage);
        //             return redirect()->back();
        //         }
        //     }
        // } catch (\Throwable $th) {
        //     Log::error('Fax sending exception', [
        //         'message' => $th->getMessage(),
        //         'file' => $th->getFile(),
        //         'line' => $th->getLine()
        //     ]);

        //     $message = 'Error sending fax: ' . $th->getMessage();
        //     session()->flash('error-message', $message);

        //     return redirect()->back();
        // }


        // DB::commit();

        // $message = 'Fax sent successfully';

        // session()->flash('success-message', $message);

        // return redirect()->back();
    }

    public function SendDispenseJob(Request $request)
    {

        // Log all request parameters for debugging
        Log::info('sendFaxAll request parameters', [
            'all_params' => $request->all(),
            'provider_id' => $request->provider_id,
            'has_provider_id' => $request->has('provider_id'),
            'medication_id' => $request->medication_id,
            'signed_date' => $request->signed_date,
            'displayed_ids' => $request->displayed_ids
        ]);

        $query = ImportFile::with('import')->where('status', ImportFile::STATUS_PENDING_APPROVAL);

        // Apply provider filter if provided
        if ($request->has('provider_id') && !empty($request->provider_id)) {
            Log::info('About to apply provider filter', ['provider_id' => $request->provider_id]);

            // Get the original SQL query for debugging
            $originalSql = $query->toSql();
            $originalBindings = $query->getBindings();
            Log::info('Original query before provider filter', ['sql' => $originalSql, 'bindings' => $originalBindings]);

            $query->whereHas('import', function ($q) use ($request) {
                $q->where('user_id', $request->provider_id);
            });

            // Get the updated SQL query for debugging
            $filteredSql = $query->toSql();
            $filteredBindings = $query->getBindings();
            Log::info('Query after provider filter', ['sql' => $filteredSql, 'bindings' => $filteredBindings]);

            Log::info('Filtering by provider in sendFaxAll', ['provider_id' => $request->provider_id]);
        } else {
            Log::warning('No provider filter applied', [
                'has_provider_id' => $request->has('provider_id'),
                'provider_id_value' => $request->provider_id
            ]);
        }

        // Apply medication filter if provided
        if ($request->has('medication_id') && !empty($request->medication_id)) {
            $medication = Medication::find($request->medication_id);
            if ($medication) {
                $query->where('medication', $medication->name);
                Log::info('Filtering by medication in sendFaxAll', ['medication_id' => $request->medication_id, 'medication_name' => $medication->name]);
            }
        }

        // Apply signed date filter if provided
        if ($request->has('signed_date') && !empty($request->signed_date)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $request->signed_date);
                $query->whereDate('signed_at', $date);
                Log::info('Filtering by signed date in sendFaxAll', ['date' => $request->signed_date]);
            } catch (\Exception $e) {
                Log::error('Invalid date format in sendFaxAll', ['date' => $request->signed_date, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . $request->search . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('file_name', 'like', $searchTerm)
                    ->orWhere('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('medication', 'like', $searchTerm);
            });
            Log::info('Filtering by search in sendFaxAll', ['search' => $request->search]);
        }

        // Get the final SQL query for debugging
        $finalSql = $query->toSql();
        $finalBindings = $query->getBindings();
        Log::info('Final query before execution', ['sql' => $finalSql, 'bindings' => $finalBindings]);

        // Determine if specific IDs are provided
        if ($request->has('displayed_ids') && is_array($request->displayed_ids)) {
            // If we have specific IDs, we need to make sure they match our filters
            // First, get all IDs that match our filters
            $filteredIds = $query->pluck('id')->toArray();

            // Then, only use the displayed_ids that are also in our filtered set
            $validDisplayedIds = array_intersect($request->displayed_ids, $filteredIds);

            Log::info('Filtering displayed_ids', [
                'original_count' => count($request->displayed_ids),
                'filtered_count' => count($validDisplayedIds),
                'original_ids' => $request->displayed_ids,
                'filtered_ids' => $validDisplayedIds
            ]);

            // Now get the records using the filtered IDs
            $import_files = ImportFile::with('import')
                ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
                ->whereIn('id', $validDisplayedIds)
                ->get();

            Log::info('Using filtered displayed_ids', [
                'count' => count($validDisplayedIds),
                'results_count' => $import_files->count()
            ]);
        } else {
            // If no specific IDs, use the query with all our filters
            $import_files = $query->get();
            Log::info('No displayed_ids provided, using all matching records', [
                'results_count' => $import_files->count()
            ]);
        }

        // Update status to Pending Dispatch before dispatching the job
        foreach ($import_files as $import_file) {
            $import_file->status = ImportFile::STATUS_PENDING_DISPATCH;
            $import_file->save();

            // Log script sending
            LogService::logScriptSent([
                'patient_name' => $import_file->first_name . ' ' . $import_file->last_name,
                'medication' => $import_file->medication,
                'script_id' => $import_file->id,
                'status' => $import_file->status
            ]);
        }

        $import_file_ids = $import_files->pluck('id')->toArray();

        // Log the IDs being sent to the job
        Log::info('Sending IDs to SendFilesToFaxJob', [
            'import_file_ids' => $import_file_ids,
            'count' => count($import_file_ids)
        ]);
        $total_files_to_send = count($import_file_ids);

        $user = Auth::user();
        SendDispenseJob::dispatch($import_file_ids, $user, [
            'total' => count($import_file_ids),
            'title' => 'Sending Scripts to Dispense Pro',
        ]);

        return redirect()->back()->with('success-message', "Dispense job has been queued successfully.");
    }

    public function showPdf(ImportFile $importFile)
    {
        if (!$importFile || !$importFile->file_path) {
            $message = __('messages.script_not_found');
            session()->flash('error-message', $message);
            return redirect()->back();
        }

        // Check if file exists on public disk first, then try default disk
        if (Storage::disk('public')->exists($importFile->file_path)) {
            return response()->file(storage_path('app/public/' . $importFile->file_path));
        } elseif (Storage::exists($importFile->file_path)) {
            return Storage::response($importFile->file_path);
        } else {
            $message = __('messages.script_not_found');
            session()->flash('error-message', $message);
            return redirect()->back();
        }
    }

    public function edit(ImportFile $importFile)
    {
        $page_title = 'Edit New';
        $has_back = request('return_url') ?: route('scripts.ready-to-sign');

        $livewire_component = 'new-import';
        $livewire_data = [
            'page_title' => $page_title,
            'importFile' => $importFile
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
            'has_back' => $has_back,
        ]);
    }

    public function returnScript(Request $request)
    {
        try {
            $importFile = ImportFile::find($request->id);
            if ($importFile) {
                $importFile->comment = $request->reason ?? 'Recall by provider';
                $importFile->returned_by_user_id = Auth::id(); // Store the ID of the operator who returned the script
                $importFile->signed_at = null;
                $importFile->status = ImportFile::STATUS_PENDING_REVISION;
                $importFile->is_eligible_for_signing = false;
                $importFile->save();

                // Log script return
                LogService::logScriptReturned([
                    'patient_name' => $importFile->first_name . ' ' . $importFile->last_name,
                    'medication' => $importFile->medication,
                    'script_id' => $importFile->id,
                    'status' => $importFile->status
                ], $request->reason ?? 'Recalled by provider');

                // $this->regeneratePdfWithoutSignature($importFile);

                // Create directory for storing PDFs if needed
                $storagePath = 'public/prescriptions/' . $importFile->import_id;
                Storage::makeDirectory($storagePath);

                // Get the logged-in user and state information
                $user = User::find($importFile->import->user_id);

                $userState = null;
                $doctorName = 'Dr. April'; // Default name

                if ($user) {
                    // Get the user's full name for the signature
                    $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);

                    // Get the user's state information if available
                    if ($user->state_id) {
                        $userState = State::find($user->state_id);
                    }
                }

                // Format dates properly
                $scriptDateFormatted = $importFile->script_date;
                if ($scriptDateFormatted && !is_string($scriptDateFormatted)) {
                    try {
                        $scriptDateFormatted = Carbon::parse($scriptDateFormatted)->format('m/d/Y');
                    } catch (\Exception) {
                        // Keep original if parsing fails
                    }
                }

                $dobFormatted = $importFile->dob;
                if ($dobFormatted && !is_string($dobFormatted)) {
                    try {
                        $dobFormatted = Carbon::parse($dobFormatted)->format('m/d/Y');
                    } catch (\Exception) {
                        // Keep original if parsing fails
                    }
                }

                // Prepare data array for PDF generation
                $data = [
                    $scriptDateFormatted, // 0: Script date
                    $importFile->last_name,   // 1: Last name
                    $importFile->first_name,  // 2: First name
                    $dobFormatted,      // 3: DOB
                    $importFile->gender,      // 4: Gender
                    $importFile->address,     // 5: Address
                    $importFile->city,        // 6: City
                    $importFile->state,       // 7: State
                    $importFile->zip,         // 8: Zip
                    $importFile->phone,       // 9: Phone
                    $importFile->medication,  // 10: Medication
                    $importFile->refills,     // 11: Refills
                    $importFile->vial_quantity, // 12: Vial quantity
                    $importFile->days_supply, // 13: Days supply
                    $importFile->sig,         // 14: Sig
                    $importFile->notes,       // 15: Notes
                    $importFile->ship_to,     // 16: Ship to
                ];

                // Generate PDF
                $pdf = PDF::loadView('pdf.new-prescription', [
                    'data' => $data,
                    'isPdfDownload' => false,
                    'user' => $user,
                    'userState' => $userState,
                    'doctorName' => $doctorName,
                    'isSigned' => false,
                ]);
                $pdf->setPaper('letter');


                // Save the PDF to storage
                Storage::put($importFile->file_path, $pdf->output());

                Log::info('Script returned for revision and data after regeneration', [
                    'import_file_id' => $importFile->id,
                    'file_name' => $importFile->file_name,
                    'file_path' => $importFile->file_path,
                    'reason' => $request->reason
                ]);
            }

            if (Auth::user()->role === User::ROLE_PROVIDER) {
                $message = __('messages.script_recalled_for_revision');
            } else {
                $message = __('messages.script_return_to_provider');
            }

            return response()->json([
                'status' => true,
                'message' => $message,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in returnScript function', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'request_id' => $request->id
            ]);

            return response()->json([
                'status' => false,
                'message' => __('messages.script_recall_error'),
            ], 500);
        }
    }

    public function void(VoidScriptRequest $request)
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            // Get the status parameter from request
            $status = $request->input('status');
            $requestedOrderIds = $request->input('orderId');

            // Initialize order IDs array
            $orderIds = [];

            // Handle based on user role
            if ($user->role === User::ROLE_PROVIDER) {
                // For providers: get all records with the passed status
                if ($status) {
                    $query = ImportFile::whereHas('import', function ($query) use ($user) {
                        $query->where('user_id', $user->id);
                    })->where('status', $status)
                        ->whereNotNull('order_id');

                    // If specific orderIds are provided, filter by them
                    if ($requestedOrderIds && is_array($requestedOrderIds) && !empty($requestedOrderIds)) {
                        $query->whereIn('order_id', $requestedOrderIds);
                    }

                    $orderIds = $query->pluck('order_id')->toArray();

                    Log::info('Provider void request', [
                        'user_id' => $user->id,
                        'status' => $status,
                        'requested_order_ids' => $requestedOrderIds,
                        'filtered_order_ids' => $orderIds,
                        'count' => count($orderIds)
                    ]);
                }
            } else {
                // For admins: get all records with 'Sent' status
                $query = ImportFile::where('status', ImportFile::STATUS_SENT)
                    ->whereNotNull('order_id');

                // If specific orderIds are provided, filter by them
                if ($requestedOrderIds && is_array($requestedOrderIds) && !empty($requestedOrderIds)) {
                    $query->whereIn('order_id', $requestedOrderIds);
                }

                $orderIds = $query->pluck('order_id')->toArray();

                Log::info('Admin or operator void request', [
                    'user_id' => $user->id,
                    'status' => ImportFile::STATUS_SENT,
                    'requested_order_ids' => $requestedOrderIds,
                    'filtered_order_ids' => $orderIds,
                    'count' => count($orderIds)
                ]);
            }

            // Log the IDs being sent to the job
            Log::info('Sending IDs to VoidScriptJob', [
                'order_ids' => $orderIds,
                'count' => count($orderIds),
                'user_role' => $user->role
            ]);

            $total_files_for_void = count($orderIds);

            if ($total_files_for_void > 0) {

                $divided_order_ids = array_chunk($orderIds, 30);

                if (!empty($divided_order_ids)) {
                    foreach ($divided_order_ids as $order_ids) {
                        // Update status to STATUS_SENT_FOR_VOIDING before dispatching the job
                        ImportFile::whereIn('order_id', $order_ids)
                            ->where('status', ImportFile::STATUS_SENT)
                            ->update(['status' => ImportFile::STATUS_SENT_FOR_VOIDING]);

                        VoidScriptJob::dispatch($order_ids, $user, [
                            'total' => count($order_ids),
                            'title' => 'Sending Scripts to Void',
                        ]);
                    }
                    Log::info('Void job has been queued in chunks.', [
                        'order_ids' => $order_ids,
                        'count' => count($order_ids)
                    ]);
                } else {
                    Log::error('No order IDs to void.');
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.no_order_ids_to_void')
                ], 400);
            }

            $message = __('messages.void_job_queued');

            session()->flash('success-message', $message);

            return redirect()->back();
        } catch (\Exception $e) {
            Log::error('Error queueing void scripts', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'order_ids' => $orderIds ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to queue void scripts'
            ], 500);
        }
    }

    public function voided()
    {
        return $this->renderScriptView('Voided', 'scripts.index-void');
    }

    public function indexVoided(GetDatatableRequest $request)
    {
        $user = Auth::user();
        $data = ImportFile::with('import');

        if ($user->role === User::ROLE_PROVIDER) {
            $data = $data->whereHas('import', fn($q) => $q->where('user_id', $user->id));
        }

        $data = $data->where('status', ImportFile::STATUS_VOIDED);

        // Copy filter/search logic from indexSent
        $sentDate = $request->input('query.query.sent_date');
        if ($sentDate && !empty($sentDate)) {
            try {
                $date = Carbon::createFromFormat('Y-m-d', $sentDate);
                $data->whereDate('sent_at', $date);
                Log::info('Filtering by sent date', ['date' => $sentDate]);
            } catch (\Exception $e) {
                Log::error('Invalid date format', ['date' => $sentDate, 'error' => $e->getMessage()]);
            }
        }
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {
            $data->whereHas('import', function ($query) use ($providerId) {
                $query->where('user_id', $providerId);
            });
            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }
        $medicationId = $request->input('query.query.medication_id');
        if ($medicationId && !empty($medicationId)) {
            $medication = Medication::find($medicationId);
            if ($medication) {
                $data->where('medication', $medication->name);
                Log::info('Filtering by medication', ['medication_id' => $medicationId, 'medication_name' => $medication->name]);
            }
        }
        $clinicName = $request->input('query.query.clinic_name');
        if ($clinicName && !empty($clinicName)) {
            $data->whereHas('import.user', function ($query) use ($clinicName) {
                $query->where('clinic_name', $clinicName);
            });
            Log::info('Filtering by clinic name', ['clinic_name' => $clinicName]);
        }
        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%';
            $gender_match = null;
            $lower_search = strtolower($search);
            if (in_array($lower_search, ['m', 'male'])) {
                $gender_match = 'M';
            } elseif (in_array($lower_search, ['f', 'female'])) {
                $gender_match = 'F';
            }
            $data->where(function ($query) use ($query_search, $gender_match) {
                $query->orWhere('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('dob', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search)
                    ->orWhere('state', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('stregnth', 'like', $query_search)
                    ->orWhere('dosing', 'like', $query_search)
                    ->orWhere('refills', 'like', $query_search)
                    ->orWhere('vial_quantity', 'like', $query_search)
                    ->orWhere('sig', 'like', $query_search)
                    ->orWhere('notes', 'like', $query_search)
                    ->orWhere('comment', 'like', $query_search)
                    ->orWhere('order_id', 'like', $query_search)
                    ->orWhere('number', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    })
                    ->orWhereHas('import.user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search)
                            ->orWhere('email', 'like', $query_search)
                            ->orWhere('first_name', 'like', $query_search)
                            ->orWhere('last_name', 'like', $query_search)
                            ->orWhere('printed_name', 'like', $query_search)
                            ->orWhere('clinic_name', 'like', $query_search)
                            ->orWhere('NPI#', 'like', $query_search)
                            ->orWhere('LIC#', 'like', $query_search)
                            ->orWhere('DEA#', 'like', $query_search)
                            ->orWhere('phone', 'like', $query_search)
                            ->orWhere('fax', 'like', $query_search)
                            ->orWhere('address', 'like', $query_search)
                            ->orWhere('city', 'like', $query_search)
                            ->orWhere('zip', 'like', $query_search)
                            ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                            ->orWhereHas('state', function ($stateQ) use ($query_search) {
                                $stateQ->where('name', 'like', $query_search);
                            });
                    });
                if ($gender_match) {
                    $query->orWhere('gender', '=', $gender_match);
                }
            });
        }
        if ($sort_order && $sort_field) {
            $importFileColumns = Schema::getColumnListing((new ImportFile())->table);
            $importColumns = Schema::getColumnListing((new Import())->table);
            if (in_array($sort_field, $importFileColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else if (in_array($sort_field, $importColumns)) {
                $data->orderBy('import.' . $sort_field, $sort_order);
            } else if ($sort_field === 'provider_name') {
                $data->join('imports', 'import_files.import_id', '=', 'imports.id')
                    ->join('users', 'imports.user_id', '=', 'users.id')
                    ->orderBy('users.first_name', $sort_order)
                    ->orderBy('users.last_name', $sort_order);
            } else {
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )->through(function ($item) {
                $item->import_file_name = $item->import->file_name ?? null;
                $provider = $item->import->user ?? null;
                $item->provider_name = $provider ? ($provider->first_name . ' ' . $provider->last_name) : 'N/A';
                return $item;
            })
        );
    }
}
