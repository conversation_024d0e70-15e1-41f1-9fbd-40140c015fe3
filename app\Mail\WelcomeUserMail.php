<?php

namespace App\Mail;

use App\Models\User;
use App\Services\LogService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class WelcomeUserMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $password;

    public function __construct(User $user, $password)
    {
        $this->user = $user;
        $this->password = $password;
    }

    public function build()
    {
        return $this->markdown('emails.welcome-user')
                    ->subject('Welcome to ' . config('app.name'));
    }

    public function failed(Throwable $exception): void
    {
        // Log the error
        LogService::logSystemError('Failed to send welcome email', [
            'message' => $this->user->email,
            'context' => $exception->getMessage()
        ]);
    }
}

