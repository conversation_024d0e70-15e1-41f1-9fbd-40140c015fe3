@php
    use App\Models\User;
@endphp
<div>
    <x-form.input.text label="Phone" labelRequired="0" model="phone" type="tel" placeholder="Enter phone number" />
    <x-form.input.text label="Fax" labelRequired="0" model="fax" type="tel" placeholder="Enter fax number" />
    <x-form.input.text label="Address" labelRequired="1" model="address" placeholder="Enter street address" />
    <x-form.input.text label="City" labelRequired="1" model="city" placeholder="Enter city" />
    <div class="form-group" wire:ignore>
        <label for="state_id" class="form-label">State</label>
        <select wire:model="state_id" id="state_id" class="form-control">
            <option value="">Select State</option>
            @foreach ($states as $state)
                <option value="{{ $state->id }}" {{ $state_id == $state->id ? 'selected' : '' }}>{{ $state->name }}
                </option>
            @endforeach
        </select>
    </div>
    <x-form.input.text label="ZIP Code" labelRequired="1" model="zip"
        placeholder="Enter ZIP code (12345 or 12345-6789)" />
</div>
@push('styles')
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        document.addEventListener("livewire:load", function() {
            // Initialize Select2 for state dropdown
            function createStateDropdown() {
                $('#state_id').select2({
                    placeholder: "Select State",
                    width: '100%'
                }).on('change', function(e) {
                    @this.set('state_id', $(e.target).val());
                });
            }

            createStateDropdown();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createStateDropdown();
            });
        });
    </script>
@endpush
