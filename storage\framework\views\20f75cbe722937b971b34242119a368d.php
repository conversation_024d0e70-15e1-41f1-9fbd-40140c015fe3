

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row mb-6">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mt-8">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="provider_filter">Provider:</label>
                    <select class="form-control" id="provider_filter">
                        <option value="">All Providers</option>
                        <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($provider->id); ?>"><?php echo e($provider->first_name); ?> <?php echo e($provider->last_name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="users_dt"></div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
    <?php echo \Illuminate\View\Factory::parentPlaceholder('styles'); ?>
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .select2-container .select2-selection--single {
            height: 38px;
            display: flex;
            align-items: center;
            padding: 6px 12px;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            height: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <?php echo \Illuminate\View\Factory::parentPlaceholder('scripts'); ?>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `<?php echo e(url('/storage')); ?>`;
        const apiRoute = `<?php echo e(route('archive_staff.api')); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";
        const deleteRoute = `<?php echo e(route('users.delete', ['::ID'])); ?>`;
        const viewRoute = `<?php echo e(route('archive_staff.show-all-pdf', ['importId' => '::ID'])); ?>`;
        const downloadRoute = `<?php echo e(route('archive.download-all-pdf', ['importId' => '::ID'])); ?>`;


        datatableElement = $('#users_dt');
        searchElement = $('#users_search');

        columnArray = [{
                field: 'file_name',
                title: `File Name`,
                width: 250,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'created_at',
                title: `Created At`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
                }
            },
            {
                field: 'provider_name',
                title: `Provider Name`,
                width: 'auto',
                sortable: true,
                autoHide: false,
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 'auto',
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `
                            <a data-id="${data.id}" class="btn btn-sm btn-clean btn-icon" id="download-all-btn" data-toggle="tooltip" title="Download All Scripts">
                                <i class="menu-icon fas fa-download"></i>
                            </a>
                            <a href="${viewRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="View Scripts">
                                <i class="menu-icon fas fa-eye"></i>
                            </a>
                            `;
                },
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        const routeTemplate = "<?php echo e(route('archive.download-all-pdf', ['importId' => '__ID__'])); ?>";

        // Handle download all button
        datatableElement.on('click', '#download-all-btn', function() {
            const importId = $(this).data('id');
            const url = routeTemplate.replace('__ID__', importId ?? '');

            const form = $('<form>', {
                method: 'POST',
                action: url
            });

            // Add CSRF token
            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Submit the form
            $('body').append(form);
            form.submit();
            form.remove();
        });

        // Add event handler for medication filter
        $('#provider_filter').on('change', function() {
            // Get current filter value
            const providerId = $('#provider_filter').val();

            // Get current search value - this will preserve the search text when changing filters
            const searchValue = $(searchElement).val() || '';

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                provider_id: providerId,
                search: searchValue,
                query: {
                    provider_id: providerId,
                    search: searchValue
                }
            });

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Initialize the datatable query parameters with empty values
        datatable.setDataSourceQuery({
            query: {
                provider_id: '',
                search: ''
            }
        });

        // Get current filter values and add them to the form
        const providerId = $('#provider_filter').val();
        if (providerId) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'provider_filter',
                value: providerId
            }));
        }

        $(document).ready(function() {
            $('#provider_filter').select2({
                width: '100%'
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/archive_staff/index.blade.php ENDPATH**/ ?>