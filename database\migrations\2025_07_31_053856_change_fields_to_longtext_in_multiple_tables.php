<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->longText('first_name')->change();
            $table->longText('last_name')->change();
            $table->longText('city')->change();
        });

        Schema::table('practices', function (Blueprint $table) {
            $table->longText('name')->change();
            $table->longText('address')->change();
            $table->longText('city')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('first_name', 255)->change();
            $table->string('last_name', 255)->change();
            $table->string('city', 255)->change();
        });

        Schema::table('practices', function (Blueprint $table) {
            $table->string('name', 255)->change();
            $table->string('address', 255)->change();
            $table->string('city', 255)->change();
        });
    }
};
