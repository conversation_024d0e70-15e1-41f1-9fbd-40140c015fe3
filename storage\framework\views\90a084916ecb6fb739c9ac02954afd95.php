<!DOCTYPE html>

<html lang="en">

<head>
    <title><?php echo e(config('app.name')); ?> | Forgot Password</title>
    <meta name="description" content="Forgot Password page" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
    <link href="<?php echo e(asset('css/pages/login/classic/login-4.css')); ?>" rel="stylesheet" type="text/css" />

    <link href="<?php echo e(asset('css/style.bundle.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/common.css')); ?>" rel="stylesheet" type="text/css" />

    <link rel="shortcut icon" href="<?php echo e(asset('images/logo.png')); ?>" />
    <style>
        /* .login-bg{
            background: url('https://source.unsplash.com/aEnH4hJ_Mrs/1920x1080');
            background-repeat: no-repeat;
            background-size: cover;
        } */
    </style>
</head>

<body id="kt_body"
    class="header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-minimize-hoverable page-loading">

    <div class="d-flex flex-column flex-root">

        <div class="login login-4 login-signin-on d-flex flex-row-fluid" id="kt_login">
            <div class="d-flex flex-center flex-row-fluid bgi-size-cover bgi-position-top bgi-no-repeat login-bg">
                <div class="login-form text-center p-7 position-relative overflow-hidden">
                    <div class="card card-body">
                        <form class="form" method="POST" action="<?php echo e(route('forgot-password-post')); ?>"
                            id="kt_login_signin_form">
                            <?php echo csrf_field(); ?>
                            <div class="login-signin">
                                <h3 class="font-weight-bolder mb-6">Forgot Password</h3>
                                <div class="form-group mb-5 text-left">
                                    <label for="email" class="form-label">Enter email to reset your password</label>
                                    <input
                                        class="form-control h-auto form-control-solid py-4 px-8 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        required type="email" placeholder="Email" name="email" id="email" />
                                    <?php if (isset($component)) { $__componentOriginal7ec225ec96001a00becbb6d24d977476 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ec225ec96001a00becbb6d24d977476 = $attributes; } ?>
<?php $component = App\View\Components\Error::resolve(['name' => 'email'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Error::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $attributes = $__attributesOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__attributesOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $component = $__componentOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__componentOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
                                </div>
                                <?php if(request()->message_sent ?? ''): ?>
                                    <span class="valid-feedback d-block">
                                        <strong>If an account with the provided email exists, you will shortly receive a
                                            temporary password.</strong>
                                    </span>
                                <?php else: ?>
                                    <button id="kt_login_signin_submit"
                                        class="btn btn-primary font-weight-bold px-9 py-4 my-3 mx-4">Reset
                                        Password</button>
                                <?php endif; ?>
                                <div class="form-group mb-0 mt-3">
                                    <a href="<?php echo e(route('login')); ?>" class="text-primary">Back to Login</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>

</html>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/auth/forgot.blade.php ENDPATH**/ ?>