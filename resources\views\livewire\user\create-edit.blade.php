@php
    use App\Models\User;
@endphp
<div>
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card>
                    <x-card.body>
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Email" labelRequired="1" model="user.email" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="First Name" labelRequired="1" model="user.first_name" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Last Name" labelRequired="1" model="user.last_name" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Printed Name" labelRequired="1" model="user.printed_name" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Clinic Name" labelRequired="0" model="user.clinic_name" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="NPI#" labelRequired="1" model="user.NPI#" type="number"
                                    placeholder="Enter NPI#" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="LIC#" labelRequired="0" model="user.LIC#"
                                    placeholder="Enter LIC#" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="DEA#" labelRequired="0" model="user.DEA#"
                                    placeholder="Enter DEA#" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Phone" labelRequired="0" model="user.phone" type="number" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Fax" labelRequired="0" model="user.fax" type="number" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.drop-down label="Default Dispatch Method" labelRequired="1"
                                    model="user.default_dispatch_method">
                                    <option value="">Select Method</option>
                                    <option value="{{ User::DISPATCH_METHOD_FAX }}"
                                        {{ $user->default_dispatch_method == User::DISPATCH_METHOD_FAX ? 'selected' : '' }}>
                                        Fax Plus
                                    </option>
                                    <option value="{{ User::DISPATCH_METHOD_DISPENSE_PRO }}"
                                        {{ $user->default_dispatch_method == User::DISPATCH_METHOD_DISPENSE_PRO ? 'selected' : '' }}>
                                        Dispense Pro</option>
                                </x-form.input.drop-down>
                            </div>

                            @if ($user['default_dispatch_method'] === User::DISPATCH_METHOD_DISPENSE_PRO)
                                <div class="col-md-12 mb-3">
                                    <x-form.input.text label="DispensePro abbreviation" labelRequired="1"
                                        model="user.dispense_abbreviation" />
                                </div>
                            @endif

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Address" labelRequired="0" model="user.address" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="City" labelRequired="0" model="user.city" />
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.drop-down label="State" labelRequired="0" model="user.state_id"
                                    placeholder="Select State">
                                    <option value="">Select State</option>
                                    @foreach ($states as $state)
                                        <option value="{{ $state->id }}"
                                            {{ $user->state_id == $state->id ? 'selected' : '' }}>{{ $state->name }}
                                        </option>
                                    @endforeach
                                </x-form.input.drop-down>
                            </div>

                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="Zip" labelRequired="0" model="user.zip" />
                            </div>
                            <x-layout.col>
                                <div class="col-6 mt-4" id="signature-container">
                                    @if ($user->signature)
                                        <div class="mb-3">
                                            <p class="mb-0">Current Signature</p>
                                            <img class="mb-3 border" style="object-fit: contain; max-width: 200px"
                                                src="/storage/{{ $user->signature }}">
                                        </div>
                                    @endif

                                    <x-form.input.image labelRequired="1" :preview="$signature" label="Upload New Signature"
                                        :previewUrl="$signature ? optional($signature)->temporaryUrl() : null" model="signature" />
                                </div>
                            </x-layout.col>
                        </div>
                    </x-card.body>

                    <x-group.errors />

                    <x-card.footer>
                        <button type="submit" id="save-button" class="btn btn-primary" wire:loading.attr="disabled"
                            wire:loading.class="disabled" wire:target="store">
                            <span id="save-text" wire:loading.remove wire:target="store">Save</span>
                            <span id="signature-uploading-text" style="display: none;">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Signature Uploading...
                            </span>
                            <span id="saving-text" wire:loading wire:target="store">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Saving...
                            </span>
                        </button>
                    </x-card.footer>
                </x-card>
            </div>
        </x-layout.row>
    </form>
</div>

@push('styles')
    <style>
        [x-cloak] {
            display: none !important;
        }

        x

        /* Ensure button maintains proper size and text visibility */
        .btn.btn-primary {
            min-width: 120px;
            position: relative;
        }

        .btn.btn-primary span {
            position: relative;
            z-index: 2;
        }

        .btn.disabled,
        .btn[disabled] {
            opacity: 0.65;
            cursor: not-allowed;
            pointer-events: none;
        }
    </style>
@endpush

@push('scripts')
    <script>
        document.addEventListener("livewire:load", function() {
            // Initialize global variable to track upload state
            window.signatureUploading = false;

            // Wait for DOM to be fully loaded
            setTimeout(function() {
                // Get elements
                const saveButton = document.getElementById('save-button');
                const saveText = document.getElementById('save-text');
                const signatureUploadingText = document.getElementById('signature-uploading-text');
                const savingText = document.getElementById('saving-text');
                const form = document.querySelector('form');

                // Function to update button text state
                function updateButtonTextState(state) {
                    // Hide all text states first
                    saveText.style.display = 'none';
                    signatureUploadingText.style.display = 'none';
                    savingText.style.display = 'none';

                    // Show only the requested state
                    if (state === 'save') {
                        saveText.style.display = 'inline-block';
                    } else if (state === 'uploading') {
                        signatureUploadingText.style.display = 'inline-block';
                    } else if (state === 'saving') {
                        savingText.style.display = 'inline-block';
                    }
                }

                // Function to disable save button during upload
                function disableSaveButton() {
                    saveButton.disabled = true;
                    saveButton.setAttribute('disabled', 'disabled');
                    saveButton.classList.add('disabled');
                    updateButtonTextState('uploading');

                    // Add form submit interceptor
                    form.addEventListener('submit', preventFormSubmit);
                }

                // Function to enable save button after upload
                function enableSaveButton() {
                    // Only enable if there's no active upload
                    if (!window.signatureUploading) {
                        saveButton.disabled = false;
                        saveButton.removeAttribute('disabled');
                        saveButton.classList.remove('disabled');
                        updateButtonTextState('save');

                        // Remove form submit interceptor
                        form.removeEventListener('submit', preventFormSubmit);
                    }
                }

                // Function to prevent form submission during upload
                function preventFormSubmit(e) {
                    if (window.signatureUploading) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                }

                // Add form submit handler to check for active uploads
                form.addEventListener('submit', function(e) {
                    if (window.signatureUploading) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Show a message to the user
                        alert('Please wait for the signature upload to complete before saving.');
                        return false;
                    }
                });

                // Find the signature input element - try multiple selectors
                let signatureInput = document.querySelector('input[type="file"][id="signature"]');

                // If not found, try alternative selectors
                if (!signatureInput) {
                    signatureInput = document.querySelector('input[type="file"][wire\\:model="signature"]');
                }

                if (!signatureInput) {
                    signatureInput = document.querySelector(
                        'input[type="file"][wire\\:model\\.defer="signature"]');
                }

                if (!signatureInput) {
                    // Last resort - get any file input in the signature container
                    signatureInput = document.querySelector('#signature-container input[type="file"]');
                }

                if (signatureInput) {
                    // Create a MutationObserver to watch for changes to the signature input
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName ===
                                'disabled') {
                                const isDisabled = signatureInput.disabled;

                                // If the input is disabled, it means an upload is in progress
                                if (isDisabled) {
                                    window.signatureUploading = true;
                                    disableSaveButton();
                                }
                            }
                        });
                    });

                    // Start observing the signature input for attribute changes
                    observer.observe(signatureInput, {
                        attributes: true
                    });

                    // Also add direct event listeners to the input
                    signatureInput.addEventListener('change', function(event) {
                        if (event.target.files && event.target.files.length > 0) {
                            console.log('File selected:', event.target.files[0].name);
                            window.signatureUploading = true;
                            disableSaveButton();
                        }
                    });
                } else {
                    console.error('Signature input not found!');
                }

                // Listen for upload progress events
                document.addEventListener('signature-upload-progress', function(event) {
                    if (event.detail && event.detail.model === 'signature') {
                        // Ensure the button stays disabled during upload
                        window.signatureUploading = true;
                        disableSaveButton();
                    }
                });

                // Listen for custom signature upload events
                document.addEventListener('signature-upload-start', function(event) {
                    if (event.detail && event.detail.model === 'signature') {
                        window.signatureUploading = true;
                        disableSaveButton();
                    }
                });

                document.addEventListener('signature-upload-finish', function(event) {
                    if (event.detail && event.detail.model === 'signature') {
                        // Use a timeout to ensure the upload is really complete
                        setTimeout(function() {
                            window.signatureUploading = false;
                            enableSaveButton();
                        }, 500);
                    }
                });

                document.addEventListener('signature-upload-error', function(event) {
                    if (event.detail && event.detail.model === 'signature') {
                        window.signatureUploading = false;
                        enableSaveButton();
                    }
                });

                // Also add global event listeners as a backup approach
                window.addEventListener('livewire-upload-start', function(event) {
                    if (event.detail && event.detail.name === 'signature') {
                        window.signatureUploading = true;
                        disableSaveButton();
                    }
                });

                window.addEventListener('livewire-upload-finish', function(event) {
                    if (event.detail && event.detail.name === 'signature') {
                        // Use a timeout to ensure the upload is really complete
                        setTimeout(function() {
                            window.signatureUploading = false;
                            enableSaveButton();
                        }, 500);
                    }
                });

                window.addEventListener('livewire-upload-error', function(event) {
                    if (event.detail && event.detail.name === 'signature') {
                        window.signatureUploading = false;
                        enableSaveButton();
                    }
                });

                // Check for Livewire loading events to handle the saving state
                document.addEventListener('livewire:load', function() {
                    Livewire.hook('message.sent', () => {
                        // Only show saving if we're not uploading
                        if (!window.signatureUploading) {
                            updateButtonTextState('saving');
                        } else {
                            updateButtonTextState('uploading');
                        }
                    });

                    Livewire.hook('message.processed', () => {
                        // Only restore save text if we're not uploading
                        if (!window.signatureUploading) {
                            updateButtonTextState('save');
                        } else {
                            updateButtonTextState('uploading');
                        }
                    });
                });
            }, 500); // Small delay to ensure DOM is loaded

            // Initialize Select2 for state dropdown
            function createStateDropdown() {
                $('#user\\.state_id').select2({
                    placeholder: "Select State",
                }).on('change', function(e) {
                    @this.set('user.state_id', $(e.target).val());
                });
            }

            // Initialize Select2 for Default Dispatch Method dropdown
            function createDispatchMethodDropdown() {
                $('#user\\.default_dispatch_method').select2({
                    placeholder: "Select Method",
                    width: '100%'
                    // Search enabled by default
                }).on('change', function(e) {
                    @this.set('user.default_dispatch_method', $(e.target).val());
                });
            }

            createStateDropdown();
            createDispatchMethodDropdown();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createStateDropdown();
                createDispatchMethodDropdown();
            });
        });
    </script>
@endpush
