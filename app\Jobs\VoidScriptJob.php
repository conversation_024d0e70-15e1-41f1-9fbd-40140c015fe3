<?php

namespace App\Jobs;

use App\Models\ImportFile;
use App\Models\User;
use App\Services\LogService;
use App\Services\ProgressTrackingService;
use App\Traits\DispenseProManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class VoidScriptJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, DispenseProManager;

    protected $orderIds;
    protected User $userId;
    public $metadata = [];

    /**
     * Create a new job instance.
     *
     * @param array $orderIds
     * @param User $user_id
     */
    public function __construct(array $orderIds, $user_id,  $metadata = [])
    {
        $this->orderIds = $orderIds;
        $this->userId = $user_id;
        $this->metadata = $metadata;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            
            $total = count($this->orderIds);
            $processed = 0;

            $progressTrackingService = new ProgressTrackingService();
            $progressTrackingService->manageProgress("queue_progress", [
                'processed' => $processed,
                'total' => $total,
            ], 30);

            // Call DispenseProVoidOrder first
            $results = self::DispenseProVoidOrder($this->orderIds);

            Log::info('VoidScriptJob results', $results);

            // Check if there are any error messages in the results
            $errorMessages = $results['errorMessages'] ?? [];
            $hasErrors = !empty($errorMessages);

            if ($hasErrors) {
                // If there are errors, don't change status and log the errors
                Log::warning('VoidScriptJob failed due to errors', [
                    'error_messages' => $errorMessages,
                    'order_ids' => $this->orderIds,
                    'results' => $results
                ]);

                // Log to system logs
                LogService::logSystemError('Script void operation failed due to errors', [
                    'error_messages' => $errorMessages,
                    'order_ids' => $this->orderIds,
                    'results' => $results
                ]);

                // Revert status back to STATUS_SENT for affected records
                ImportFile::whereIn('order_id', $this->orderIds)
                    ->where('status', ImportFile::STATUS_SENT_FOR_VOIDING)
                    ->update(['status' => ImportFile::STATUS_SENT]);

                return;
            }

            // Only change status for each orderId based on its result
            $voided = [];
            $failed = [];
            foreach ($this->orderIds as $orderId) {
                $importFile = ImportFile::where('order_id', $orderId)
                    ->where('status', ImportFile::STATUS_SENT_FOR_VOIDING)
                    ->first();

                if (!$importFile) continue;

                // Find the result for this orderId
                $orderResult = null;
                if (isset($results['responses']) && is_array($results['responses'])) {
                    // If responses are keyed by orderId
                    $orderResult = $results['responses'][$orderId] ?? null;
                } elseif (isset($results[$orderId])) {
                    // If results are keyed directly
                    $orderResult = $results[$orderId];
                } elseif (isset($results['orderId']) && $results['orderId'] === $orderId) {
                    // If single result
                    $orderResult = $results;
                }

                // Fallback: try to find in a flat array
                if (!$orderResult && isset($results[0]) && is_array($results[0])) {
                    foreach ($results as $res) {
                        if (isset($res['orderId']) && $res['orderId'] === $orderId) {
                            $orderResult = $res;
                            break;
                        }
                    }
                }

                // Default: treat as failed if not found
                $status = $orderResult['status'] ?? null;
                if ($status == 200) {
                    $importFile->status = ImportFile::STATUS_VOIDED;
                    $voided[] = $importFile->order_id;
                } else {
                    $importFile->status = ImportFile::STATUS_SENT;
                    $failed[] = $importFile->order_id;
                }
                $importFile->save();
            }

            // Log successful void operation to system logs
            LogService::logUserAction('script_voided', 'Scripts voided successfully', [
                'voided_order_ids' => $voided,
                'failed_order_ids' => $failed,
                'results' => $results,
                'message' => count($voided) . ' script(s) voided, ' . count($failed) . ' failed.'
            ], $this->userId);

            Log::info('VoidScriptJob completed', [
                'voided_order_ids' => $voided,
                'failed_order_ids' => $failed,
                'results' => $results,
                'message' => count($voided) . ' script(s) voided, ' . count($failed) . ' failed.',
                'user_id' => $this->userId ? $this->userId->id : null
            ]);
        } catch (\Exception $e) {
            Log::error('Error in VoidScriptJob', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'order_ids' => $this->orderIds,
                'user_id' => $this->userId ? $this->userId->id : null
            ]);

            // Log to system logs
            LogService::logSystemError('VoidScriptJob exception occurred', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'order_ids' => $this->orderIds,
                'user_id' => $this->userId ? $this->userId->id : null
            ]);
        }
    }
}
